{"logs": [{"outputFile": "com.anonymous.myapp-mergeDebugResources-55:/values-b+sr+Latn/values-b+sr+Latn.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.13/transforms/1cc5e9e2cf9bf5411b10463ffe5bdf1d/transformed/appcompat-1.7.0/res/values-b+sr+Latn/values-b+sr+Latn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,419,505,609,731,816,898,989,1082,1177,1271,1371,1464,1559,1664,1755,1846,1932,2037,2143,2246,2353,2462,2569,2739,2836", "endColumns": "106,100,105,85,103,121,84,81,90,92,94,93,99,92,94,104,90,90,85,104,105,102,106,108,106,169,96,86", "endOffsets": "207,308,414,500,604,726,811,893,984,1077,1172,1266,1366,1459,1554,1659,1750,1841,1927,2032,2138,2241,2348,2457,2564,2734,2831,2918"}, "to": {"startLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,117", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "374,481,582,688,774,878,1000,1085,1167,1258,1351,1446,1540,1640,1733,1828,1933,2024,2115,2201,2306,2412,2515,2622,2731,2838,3008,10062", "endColumns": "106,100,105,85,103,121,84,81,90,92,94,93,99,92,94,104,90,90,85,104,105,102,106,108,106,169,96,86", "endOffsets": "476,577,683,769,873,995,1080,1162,1253,1346,1441,1535,1635,1728,1823,1928,2019,2110,2196,2301,2407,2510,2617,2726,2833,3003,3100,10144"}}, {"source": "/Users/<USER>/.gradle/caches/8.13/transforms/25ad4bdeddd79d498a0e9dfe4af5a7d2/transformed/core-1.13.1/res/values-b+sr+Latn/values-b+sr+Latn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,352,456,560,665,781", "endColumns": "97,101,96,103,103,104,115,100", "endOffsets": "148,250,347,451,555,660,776,877"}, "to": {"startLines": "39,40,41,42,43,44,45,121", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3541,3639,3741,3838,3942,4046,4151,10385", "endColumns": "97,101,96,103,103,104,115,100", "endOffsets": "3634,3736,3833,3937,4041,4146,4262,10481"}}, {"source": "/Users/<USER>/.gradle/caches/8.13/transforms/44d5193287b18dec49d9cd0bdd73b6e9/transformed/browser-1.6.0/res/values-b+sr+Latn/values-b+sr+Latn.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,166,266,379", "endColumns": "110,99,112,97", "endOffsets": "161,261,374,472"}, "to": {"startLines": "49,53,54,55", "startColumns": "4,4,4,4", "startOffsets": "4574,4907,5007,5120", "endColumns": "110,99,112,97", "endOffsets": "4680,5002,5115,5213"}}, {"source": "/Users/<USER>/.gradle/caches/8.13/transforms/1688e4f4998421f6cdbc59f17cef2f61/transformed/material-1.12.0/res/values-b+sr+Latn/values-b+sr+Latn.xml", "from": {"startLines": "2,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,324,401,478,558,666,760,854,986,1067,1130,1196,1289,1357,1420,1523,1583,1649,1705,1776,1836,1890,2002,2059,2120,2174,2250,2375,2462,2539,2632,2716,2799,2938,3020,3103,3234,3322,3400,3454,3510,3576,3650,3728,3799,3881,3957,4033,4108,4180,4287,4377,4450,4542,4638,4710,4786,4882,4935,5017,5084,5171,5258,5320,5384,5447,5516,5621,5731,5827,5935,5993,6053,6133,6216,6292", "endLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "endColumns": "12,76,76,79,107,93,93,131,80,62,65,92,67,62,102,59,65,55,70,59,53,111,56,60,53,75,124,86,76,92,83,82,138,81,82,130,87,77,53,55,65,73,77,70,81,75,75,74,71,106,89,72,91,95,71,75,95,52,81,66,86,86,61,63,62,68,104,109,95,107,57,59,79,82,75,76", "endOffsets": "319,396,473,553,661,755,849,981,1062,1125,1191,1284,1352,1415,1518,1578,1644,1700,1771,1831,1885,1997,2054,2115,2169,2245,2370,2457,2534,2627,2711,2794,2933,3015,3098,3229,3317,3395,3449,3505,3571,3645,3723,3794,3876,3952,4028,4103,4175,4282,4372,4445,4537,4633,4705,4781,4877,4930,5012,5079,5166,5253,5315,5379,5442,5511,5616,5726,5822,5930,5988,6048,6128,6211,6287,6364"}, "to": {"startLines": "2,34,35,36,37,38,46,47,48,50,51,52,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,118,119,120", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3105,3182,3259,3339,3447,4267,4361,4493,4685,4748,4814,5218,5286,5349,5452,5512,5578,5634,5705,5765,5819,5931,5988,6049,6103,6179,6304,6391,6468,6561,6645,6728,6867,6949,7032,7163,7251,7329,7383,7439,7505,7579,7657,7728,7810,7886,7962,8037,8109,8216,8306,8379,8471,8567,8639,8715,8811,8864,8946,9013,9100,9187,9249,9313,9376,9445,9550,9660,9756,9864,9922,9982,10149,10232,10308", "endLines": "6,34,35,36,37,38,46,47,48,50,51,52,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,118,119,120", "endColumns": "12,76,76,79,107,93,93,131,80,62,65,92,67,62,102,59,65,55,70,59,53,111,56,60,53,75,124,86,76,92,83,82,138,81,82,130,87,77,53,55,65,73,77,70,81,75,75,74,71,106,89,72,91,95,71,75,95,52,81,66,86,86,61,63,62,68,104,109,95,107,57,59,79,82,75,76", "endOffsets": "369,3177,3254,3334,3442,3536,4356,4488,4569,4743,4809,4902,5281,5344,5447,5507,5573,5629,5700,5760,5814,5926,5983,6044,6098,6174,6299,6386,6463,6556,6640,6723,6862,6944,7027,7158,7246,7324,7378,7434,7500,7574,7652,7723,7805,7881,7957,8032,8104,8211,8301,8374,8466,8562,8634,8710,8806,8859,8941,9008,9095,9182,9244,9308,9371,9440,9545,9655,9751,9859,9917,9977,10057,10227,10303,10380"}}]}]}