{"logs": [{"outputFile": "com.anonymous.myapp-mergeDebugResources-55:/values-cs/values-cs.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.13/transforms/44d5193287b18dec49d9cd0bdd73b6e9/transformed/browser-1.6.0/res/values-cs/values-cs.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,157,260,374", "endColumns": "101,102,113,100", "endOffsets": "152,255,369,470"}, "to": {"startLines": "52,56,57,58", "startColumns": "4,4,4,4", "startOffsets": "4725,5052,5155,5269", "endColumns": "101,102,113,100", "endOffsets": "4822,5150,5264,5365"}}, {"source": "/Users/<USER>/.gradle/caches/8.13/transforms/1cc5e9e2cf9bf5411b10463ffe5bdf1d/transformed/appcompat-1.7.0/res/values-cs/values-cs.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,314,424,510,615,732,810,886,977,1070,1165,1259,1353,1446,1541,1638,1729,1820,1904,2008,2120,2219,2325,2436,2538,2701,2799", "endColumns": "106,101,109,85,104,116,77,75,90,92,94,93,93,92,94,96,90,90,83,103,111,98,105,110,101,162,97,82", "endOffsets": "207,309,419,505,610,727,805,881,972,1065,1160,1254,1348,1441,1536,1633,1724,1815,1899,2003,2115,2214,2320,2431,2533,2696,2794,2877"}, "to": {"startLines": "8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,131", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "418,525,627,737,823,928,1045,1123,1199,1290,1383,1478,1572,1666,1759,1854,1951,2042,2133,2217,2321,2433,2532,2638,2749,2851,3014,11036", "endColumns": "106,101,109,85,104,116,77,75,90,92,94,93,93,92,94,96,90,90,83,103,111,98,105,110,101,162,97,82", "endOffsets": "520,622,732,818,923,1040,1118,1194,1285,1378,1473,1567,1661,1754,1849,1946,2037,2128,2212,2316,2428,2527,2633,2744,2846,3009,3107,11114"}}, {"source": "/Users/<USER>/.gradle/caches/8.13/transforms/1688e4f4998421f6cdbc59f17cef2f61/transformed/material-1.12.0/res/values-cs/values-cs.xml", "from": {"startLines": "2,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,368,446,524,601,704,796,888,1014,1095,1156,1221,1320,1396,1457,1546,1610,1677,1731,1799,1859,1913,2030,2090,2152,2206,2278,2400,2484,2563,2657,2740,2832,2969,3047,3129,3256,3344,3424,3478,3529,3595,3667,3744,3815,3896,3968,4045,4119,4190,4295,4383,4454,4547,4642,4716,4790,4886,4938,5021,5088,5174,5262,5324,5388,5451,5519,5629,5735,5834,5948,6006,6061,6140,6223,6298", "endLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82", "endColumns": "12,77,77,76,102,91,91,125,80,60,64,98,75,60,88,63,66,53,67,59,53,116,59,61,53,71,121,83,78,93,82,91,136,77,81,126,87,79,53,50,65,71,76,70,80,71,76,73,70,104,87,70,92,94,73,73,95,51,82,66,85,87,61,63,62,67,109,105,98,113,57,54,78,82,74,78", "endOffsets": "363,441,519,596,699,791,883,1009,1090,1151,1216,1315,1391,1452,1541,1605,1672,1726,1794,1854,1908,2025,2085,2147,2201,2273,2395,2479,2558,2652,2735,2827,2964,3042,3124,3251,3339,3419,3473,3524,3590,3662,3739,3810,3891,3963,4040,4114,4185,4290,4378,4449,4542,4637,4711,4785,4881,4933,5016,5083,5169,5257,5319,5383,5446,5514,5624,5730,5829,5943,6001,6056,6135,6218,6293,6372"}, "to": {"startLines": "2,36,37,38,39,40,48,49,50,53,54,55,60,63,65,66,67,68,69,70,71,72,73,74,75,76,77,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,132,133,134", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3184,3262,3340,3417,3520,4343,4435,4561,4827,4888,4953,5441,5675,5804,5893,5957,6024,6078,6146,6206,6260,6377,6437,6499,6553,6625,6978,7062,7141,7235,7318,7410,7547,7625,7707,7834,7922,8002,8056,8107,8173,8245,8322,8393,8474,8546,8623,8697,8768,8873,8961,9032,9125,9220,9294,9368,9464,9516,9599,9666,9752,9840,9902,9966,10029,10097,10207,10313,10412,10526,10584,10639,11119,11202,11277", "endLines": "7,36,37,38,39,40,48,49,50,53,54,55,60,63,65,66,67,68,69,70,71,72,73,74,75,76,77,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,132,133,134", "endColumns": "12,77,77,76,102,91,91,125,80,60,64,98,75,60,88,63,66,53,67,59,53,116,59,61,53,71,121,83,78,93,82,91,136,77,81,126,87,79,53,50,65,71,76,70,80,71,76,73,70,104,87,70,92,94,73,73,95,51,82,66,85,87,61,63,62,67,109,105,98,113,57,54,78,82,74,78", "endOffsets": "413,3257,3335,3412,3515,3607,4430,4556,4637,4883,4948,5047,5512,5731,5888,5952,6019,6073,6141,6201,6255,6372,6432,6494,6548,6620,6742,7057,7136,7230,7313,7405,7542,7620,7702,7829,7917,7997,8051,8102,8168,8240,8317,8388,8469,8541,8618,8692,8763,8868,8956,9027,9120,9215,9289,9363,9459,9511,9594,9661,9747,9835,9897,9961,10024,10092,10202,10308,10407,10521,10579,10634,10713,11197,11272,11351"}}, {"source": "/Users/<USER>/.gradle/caches/8.13/transforms/ee3752888ecf2d82fe4920d23f93a394/transformed/react-android-0.79.4-debug/res/values-cs/values-cs.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,127,210,281,352,439,507,577,656,738,824,910,980,1056,1133,1215,1296,1378,1453,1524,1594,1678,1751,1829,1900", "endColumns": "71,82,70,70,86,67,69,78,81,85,85,69,75,76,81,80,81,74,70,69,83,72,77,70,79", "endOffsets": "122,205,276,347,434,502,572,651,733,819,905,975,1051,1128,1210,1291,1373,1448,1519,1589,1673,1746,1824,1895,1975"}, "to": {"startLines": "35,51,59,61,62,64,78,79,80,127,128,129,130,135,136,137,138,139,140,141,142,144,145,146,147", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3112,4642,5370,5517,5588,5736,6747,6817,6896,10718,10804,10890,10960,11356,11433,11515,11596,11678,11753,11824,11894,12079,12152,12230,12301", "endColumns": "71,82,70,70,86,67,69,78,81,85,85,69,75,76,81,80,81,74,70,69,83,72,77,70,79", "endOffsets": "3179,4720,5436,5583,5670,5799,6812,6891,6973,10799,10885,10955,11031,11428,11510,11591,11673,11748,11819,11889,11973,12147,12225,12296,12376"}}, {"source": "/Users/<USER>/.gradle/caches/8.13/transforms/25ad4bdeddd79d498a0e9dfe4af5a7d2/transformed/core-1.13.1/res/values-cs/values-cs.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,356,455,560,667,786", "endColumns": "97,101,100,98,104,106,118,100", "endOffsets": "148,250,351,450,555,662,781,882"}, "to": {"startLines": "41,42,43,44,45,46,47,143", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3612,3710,3812,3913,4012,4117,4224,11978", "endColumns": "97,101,100,98,104,106,118,100", "endOffsets": "3705,3807,3908,4007,4112,4219,4338,12074"}}]}]}