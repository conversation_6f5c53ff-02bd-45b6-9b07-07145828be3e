{"logs": [{"outputFile": "com.anonymous.myapp-mergeDebugResources-55:/values-mr/values-mr.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.13/transforms/ee3752888ecf2d82fe4920d23f93a394/transformed/react-android-0.79.4-debug/res/values-mr/values-mr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,124,203,272,343,425,492,559,633,709,789,869,937,1020,1102,1177,1263,1350,1425,1496,1567,1658,1730,1805,1874", "endColumns": "68,78,68,70,81,66,66,73,75,79,79,67,82,81,74,85,86,74,70,70,90,71,74,68,72", "endOffsets": "119,198,267,338,420,487,554,628,704,784,864,932,1015,1097,1172,1258,1345,1420,1491,1562,1653,1725,1800,1869,1942"}, "to": {"startLines": "33,49,57,59,60,62,76,77,78,125,126,127,128,133,134,135,136,137,138,139,140,142,143,144,145", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2999,4530,5235,5369,5440,5581,6579,6646,6720,10545,10625,10705,10773,11171,11253,11328,11414,11501,11576,11647,11718,11910,11982,12057,12126", "endColumns": "68,78,68,70,81,66,66,73,75,79,79,67,82,81,74,85,86,74,70,70,90,71,74,68,72", "endOffsets": "3063,4604,5299,5435,5517,5643,6641,6715,6791,10620,10700,10768,10851,11248,11323,11409,11496,11571,11642,11713,11804,11977,12052,12121,12194"}}, {"source": "/Users/<USER>/.gradle/caches/8.13/transforms/25ad4bdeddd79d498a0e9dfe4af5a7d2/transformed/core-1.13.1/res/values-mr/values-mr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,155,259,360,463,565,670,787", "endColumns": "99,103,100,102,101,104,116,100", "endOffsets": "150,254,355,458,560,665,782,883"}, "to": {"startLines": "39,40,41,42,43,44,45,141", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3500,3600,3704,3805,3908,4010,4115,11809", "endColumns": "99,103,100,102,101,104,116,100", "endOffsets": "3595,3699,3800,3903,4005,4110,4227,11905"}}, {"source": "/Users/<USER>/.gradle/caches/8.13/transforms/1cc5e9e2cf9bf5411b10463ffe5bdf1d/transformed/appcompat-1.7.0/res/values-mr/values-mr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,322,429,519,620,732,810,887,978,1071,1164,1261,1361,1454,1549,1643,1734,1825,1905,2012,2113,2210,2319,2421,2535,2692,2795", "endColumns": "110,105,106,89,100,111,77,76,90,92,92,96,99,92,94,93,90,90,79,106,100,96,108,101,113,156,102,79", "endOffsets": "211,317,424,514,615,727,805,882,973,1066,1159,1256,1356,1449,1544,1638,1729,1820,1900,2007,2108,2205,2314,2416,2530,2687,2790,2870"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,129", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "309,420,526,633,723,824,936,1014,1091,1182,1275,1368,1465,1565,1658,1753,1847,1938,2029,2109,2216,2317,2414,2523,2625,2739,2896,10856", "endColumns": "110,105,106,89,100,111,77,76,90,92,92,96,99,92,94,93,90,90,79,106,100,96,108,101,113,156,102,79", "endOffsets": "415,521,628,718,819,931,1009,1086,1177,1270,1363,1460,1560,1653,1748,1842,1933,2024,2104,2211,2312,2409,2518,2620,2734,2891,2994,10931"}}, {"source": "/Users/<USER>/.gradle/caches/8.13/transforms/1688e4f4998421f6cdbc59f17cef2f61/transformed/material-1.12.0/res/values-mr/values-mr.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,259,344,431,514,607,691,791,907,989,1046,1109,1200,1265,1324,1412,1474,1536,1596,1663,1726,1780,1894,1951,2012,2066,2136,2255,2336,2413,2502,2584,2669,2804,2881,2958,3099,3185,3269,3325,3377,3443,3513,3591,3662,3744,3814,3890,3961,4030,4144,4240,4314,4412,4508,4582,4652,4754,4809,4897,4964,5051,5144,5207,5271,5334,5400,5500,5609,5703,5810,5870,5926,6004,6088,6166", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,84,86,82,92,83,99,115,81,56,62,90,64,58,87,61,61,59,66,62,53,113,56,60,53,69,118,80,76,88,81,84,134,76,76,140,85,83,55,51,65,69,77,70,81,69,75,70,68,113,95,73,97,95,73,69,101,54,87,66,86,92,62,63,62,65,99,108,93,106,59,55,77,83,77,72", "endOffsets": "254,339,426,509,602,686,786,902,984,1041,1104,1195,1260,1319,1407,1469,1531,1591,1658,1721,1775,1889,1946,2007,2061,2131,2250,2331,2408,2497,2579,2664,2799,2876,2953,3094,3180,3264,3320,3372,3438,3508,3586,3657,3739,3809,3885,3956,4025,4139,4235,4309,4407,4503,4577,4647,4749,4804,4892,4959,5046,5139,5202,5266,5329,5395,5495,5604,5698,5805,5865,5921,5999,6083,6161,6234"}, "to": {"startLines": "2,34,35,36,37,38,46,47,48,51,52,53,58,61,63,64,65,66,67,68,69,70,71,72,73,74,75,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,130,131,132", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3068,3153,3240,3323,3416,4232,4332,4448,4710,4767,4830,5304,5522,5648,5736,5798,5860,5920,5987,6050,6104,6218,6275,6336,6390,6460,6796,6877,6954,7043,7125,7210,7345,7422,7499,7640,7726,7810,7866,7918,7984,8054,8132,8203,8285,8355,8431,8502,8571,8685,8781,8855,8953,9049,9123,9193,9295,9350,9438,9505,9592,9685,9748,9812,9875,9941,10041,10150,10244,10351,10411,10467,10936,11020,11098", "endLines": "5,34,35,36,37,38,46,47,48,51,52,53,58,61,63,64,65,66,67,68,69,70,71,72,73,74,75,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,130,131,132", "endColumns": "12,84,86,82,92,83,99,115,81,56,62,90,64,58,87,61,61,59,66,62,53,113,56,60,53,69,118,80,76,88,81,84,134,76,76,140,85,83,55,51,65,69,77,70,81,69,75,70,68,113,95,73,97,95,73,69,101,54,87,66,86,92,62,63,62,65,99,108,93,106,59,55,77,83,77,72", "endOffsets": "304,3148,3235,3318,3411,3495,4327,4443,4525,4762,4825,4916,5364,5576,5731,5793,5855,5915,5982,6045,6099,6213,6270,6331,6385,6455,6574,6872,6949,7038,7120,7205,7340,7417,7494,7635,7721,7805,7861,7913,7979,8049,8127,8198,8280,8350,8426,8497,8566,8680,8776,8850,8948,9044,9118,9188,9290,9345,9433,9500,9587,9680,9743,9807,9870,9936,10036,10145,10239,10346,10406,10462,10540,11015,11093,11166"}}, {"source": "/Users/<USER>/.gradle/caches/8.13/transforms/44d5193287b18dec49d9cd0bdd73b6e9/transformed/browser-1.6.0/res/values-mr/values-mr.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,156,257,368", "endColumns": "100,100,110,101", "endOffsets": "151,252,363,465"}, "to": {"startLines": "50,54,55,56", "startColumns": "4,4,4,4", "startOffsets": "4609,4921,5022,5133", "endColumns": "100,100,110,101", "endOffsets": "4705,5017,5128,5230"}}]}]}