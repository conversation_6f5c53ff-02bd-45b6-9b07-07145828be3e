{"logs": [{"outputFile": "com.anonymous.myapp-mergeDebugResources-55:/values-da/values-da.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.13/transforms/44d5193287b18dec49d9cd0bdd73b6e9/transformed/browser-1.6.0/res/values-da/values-da.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,167,266,373", "endColumns": "111,98,106,96", "endOffsets": "162,261,368,465"}, "to": {"startLines": "50,54,55,56", "startColumns": "4,4,4,4", "startOffsets": "4596,4917,5016,5123", "endColumns": "111,98,106,96", "endOffsets": "4703,5011,5118,5215"}}, {"source": "/Users/<USER>/.gradle/caches/8.13/transforms/1688e4f4998421f6cdbc59f17cef2f61/transformed/material-1.12.0/res/values-da/values-da.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,272,350,426,504,601,681,781,930,1008,1067,1131,1217,1290,1350,1437,1501,1563,1625,1693,1758,1812,1930,1988,2049,2105,2180,2306,2392,2469,2560,2644,2724,2865,2943,3023,3145,3231,3309,3365,3416,3482,3550,3624,3695,3770,3842,3920,3990,4063,4167,4251,4328,4416,4505,4579,4652,4737,4786,4864,4930,5010,5093,5155,5219,5282,5351,5459,5562,5663,5762,5822,5877,5957,6037,6115", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,77,75,77,96,79,99,148,77,58,63,85,72,59,86,63,61,61,67,64,53,117,57,60,55,74,125,85,76,90,83,79,140,77,79,121,85,77,55,50,65,67,73,70,74,71,77,69,72,103,83,76,87,88,73,72,84,48,77,65,79,82,61,63,62,68,107,102,100,98,59,54,79,79,77,76", "endOffsets": "267,345,421,499,596,676,776,925,1003,1062,1126,1212,1285,1345,1432,1496,1558,1620,1688,1753,1807,1925,1983,2044,2100,2175,2301,2387,2464,2555,2639,2719,2860,2938,3018,3140,3226,3304,3360,3411,3477,3545,3619,3690,3765,3837,3915,3985,4058,4162,4246,4323,4411,4500,4574,4647,4732,4781,4859,4925,5005,5088,5150,5214,5277,5346,5454,5557,5658,5757,5817,5872,5952,6032,6110,6187"}, "to": {"startLines": "2,34,35,36,37,38,46,47,48,51,52,53,58,61,62,63,64,65,66,67,68,69,70,71,72,73,74,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,128,129,130", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3050,3128,3204,3282,3379,4186,4286,4435,4708,4767,4831,5295,5522,5582,5669,5733,5795,5857,5925,5990,6044,6162,6220,6281,6337,6412,6689,6775,6852,6943,7027,7107,7248,7326,7406,7528,7614,7692,7748,7799,7865,7933,8007,8078,8153,8225,8303,8373,8446,8550,8634,8711,8799,8888,8962,9035,9120,9169,9247,9313,9393,9476,9538,9602,9665,9734,9842,9945,10046,10145,10205,10260,10728,10808,10886", "endLines": "5,34,35,36,37,38,46,47,48,51,52,53,58,61,62,63,64,65,66,67,68,69,70,71,72,73,74,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,128,129,130", "endColumns": "12,77,75,77,96,79,99,148,77,58,63,85,72,59,86,63,61,61,67,64,53,117,57,60,55,74,125,85,76,90,83,79,140,77,79,121,85,77,55,50,65,67,73,70,74,71,77,69,72,103,83,76,87,88,73,72,84,48,77,65,79,82,61,63,62,68,107,102,100,98,59,54,79,79,77,76", "endOffsets": "317,3123,3199,3277,3374,3454,4281,4430,4508,4762,4826,4912,5363,5577,5664,5728,5790,5852,5920,5985,6039,6157,6215,6276,6332,6407,6533,6770,6847,6938,7022,7102,7243,7321,7401,7523,7609,7687,7743,7794,7860,7928,8002,8073,8148,8220,8298,8368,8441,8545,8629,8706,8794,8883,8957,9030,9115,9164,9242,9308,9388,9471,9533,9597,9660,9729,9837,9940,10041,10140,10200,10255,10335,10803,10881,10958"}}, {"source": "/Users/<USER>/.gradle/caches/8.13/transforms/ee3752888ecf2d82fe4920d23f93a394/transformed/react-android-0.79.4-debug/res/values-da/values-da.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,131,214,289,360,443,518,594,675,755,824,902,981,1057,1137,1217,1294,1365,1435,1518,1592,1674", "endColumns": "75,82,74,70,82,74,75,80,79,68,77,78,75,79,79,76,70,69,82,73,81,78", "endOffsets": "126,209,284,355,438,513,589,670,750,819,897,976,1052,1132,1212,1289,1360,1430,1513,1587,1669,1748"}, "to": {"startLines": "33,49,57,59,60,75,76,123,124,125,126,131,132,133,134,135,136,137,138,140,141,142", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2974,4513,5220,5368,5439,6538,6613,10340,10421,10501,10570,10963,11042,11118,11198,11278,11355,11426,11496,11680,11754,11836", "endColumns": "75,82,74,70,82,74,75,80,79,68,77,78,75,79,79,76,70,69,82,73,81,78", "endOffsets": "3045,4591,5290,5434,5517,6608,6684,10416,10496,10565,10643,11037,11113,11193,11273,11350,11421,11491,11574,11749,11831,11910"}}, {"source": "/Users/<USER>/.gradle/caches/8.13/transforms/1cc5e9e2cf9bf5411b10463ffe5bdf1d/transformed/appcompat-1.7.0/res/values-da/values-da.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,205,299,415,500,600,713,791,867,958,1051,1144,1238,1332,1425,1520,1618,1709,1800,1879,1987,2094,2190,2303,2406,2507,2660,2757", "endColumns": "99,93,115,84,99,112,77,75,90,92,92,93,93,92,94,97,90,90,78,107,106,95,112,102,100,152,96,79", "endOffsets": "200,294,410,495,595,708,786,862,953,1046,1139,1233,1327,1420,1515,1613,1704,1795,1874,1982,2089,2185,2298,2401,2502,2655,2752,2832"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,127", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "322,422,516,632,717,817,930,1008,1084,1175,1268,1361,1455,1549,1642,1737,1835,1926,2017,2096,2204,2311,2407,2520,2623,2724,2877,10648", "endColumns": "99,93,115,84,99,112,77,75,90,92,92,93,93,92,94,97,90,90,78,107,106,95,112,102,100,152,96,79", "endOffsets": "417,511,627,712,812,925,1003,1079,1170,1263,1356,1450,1544,1637,1732,1830,1921,2012,2091,2199,2306,2402,2515,2618,2719,2872,2969,10723"}}, {"source": "/Users/<USER>/.gradle/caches/8.13/transforms/25ad4bdeddd79d498a0e9dfe4af5a7d2/transformed/core-1.13.1/res/values-da/values-da.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,350,448,555,664,782", "endColumns": "95,101,96,97,106,108,117,100", "endOffsets": "146,248,345,443,550,659,777,878"}, "to": {"startLines": "39,40,41,42,43,44,45,139", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3459,3555,3657,3754,3852,3959,4068,11579", "endColumns": "95,101,96,97,106,108,117,100", "endOffsets": "3550,3652,3749,3847,3954,4063,4181,11675"}}]}]}