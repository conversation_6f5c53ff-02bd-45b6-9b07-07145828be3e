com.anonymous.myapp-viewpager2-1.0.0-0 /Users/<USER>/.gradle/caches/8.13/transforms/0a985a726a0ed91053e59647f893676a/transformed/viewpager2-1.0.0/res
com.anonymous.myapp-savedstate-1.2.1-1 /Users/<USER>/.gradle/caches/8.13/transforms/0b7ca141aa9d3a05c286852a98884a1f/transformed/savedstate-1.2.1/res
com.anonymous.myapp-androidsvg-aar-1.4-2 /Users/<USER>/.gradle/caches/8.13/transforms/0ced2c618cc0851d3ee11456ab9ae13d/transformed/androidsvg-aar-1.4/res
com.anonymous.myapp-appcompat-resources-1.7.0-3 /Users/<USER>/.gradle/caches/8.13/transforms/0ea8215bc09ef74c628f4e4a0402513e/transformed/appcompat-resources-1.7.0/res
com.anonymous.myapp-lifecycle-livedata-core-ktx-2.6.2-4 /Users/<USER>/.gradle/caches/8.13/transforms/10b213ecea0e4e52da532b6ba0f83370/transformed/lifecycle-livedata-core-ktx-2.6.2/res
com.anonymous.myapp-material-1.12.0-5 /Users/<USER>/.gradle/caches/8.13/transforms/1688e4f4998421f6cdbc59f17cef2f61/transformed/material-1.12.0/res
com.anonymous.myapp-media-1.0.0-6 /Users/<USER>/.gradle/caches/8.13/transforms/17e95e9d548b080f2342fe4e9d66b27c/transformed/media-1.0.0/res
com.anonymous.myapp-tracing-ktx-1.2.0-7 /Users/<USER>/.gradle/caches/8.13/transforms/1bbf8401d34e5d62926e60ab182c1f96/transformed/tracing-ktx-1.2.0/res
com.anonymous.myapp-appcompat-1.7.0-8 /Users/<USER>/.gradle/caches/8.13/transforms/1cc5e9e2cf9bf5411b10463ffe5bdf1d/transformed/appcompat-1.7.0/res
com.anonymous.myapp-expo.modules.systemui-5.0.9-9 /Users/<USER>/.gradle/caches/8.13/transforms/242ac0c339cf89d88a6b948ccf19dfd1/transformed/expo.modules.systemui-5.0.9/res
com.anonymous.myapp-core-1.13.1-10 /Users/<USER>/.gradle/caches/8.13/transforms/25ad4bdeddd79d498a0e9dfe4af5a7d2/transformed/core-1.13.1/res
com.anonymous.myapp-drawee-3.6.0-11 /Users/<USER>/.gradle/caches/8.13/transforms/2f42596c4ca5d2009892461f9c7813b2/transformed/drawee-3.6.0/res
com.anonymous.myapp-profileinstaller-1.3.1-12 /Users/<USER>/.gradle/caches/8.13/transforms/3070f604e3c80036d56e6e100694a483/transformed/profileinstaller-1.3.1/res
com.anonymous.myapp-core-ktx-1.13.1-13 /Users/<USER>/.gradle/caches/8.13/transforms/44982b7d7b05239c12187164d2522afe/transformed/core-ktx-1.13.1/res
com.anonymous.myapp-browser-1.6.0-14 /Users/<USER>/.gradle/caches/8.13/transforms/44d5193287b18dec49d9cd0bdd73b6e9/transformed/browser-1.6.0/res
com.anonymous.myapp-fragment-ktx-1.6.1-15 /Users/<USER>/.gradle/caches/8.13/transforms/45a5fcb4b05df7123a46aa86205607f0/transformed/fragment-ktx-1.6.1/res
com.anonymous.myapp-recyclerview-1.1.0-16 /Users/<USER>/.gradle/caches/8.13/transforms/47a56a4f1be0e9a7d815855bffa6881d/transformed/recyclerview-1.1.0/res
com.anonymous.myapp-constraintlayout-2.0.1-17 /Users/<USER>/.gradle/caches/8.13/transforms/4a6ff7a886435c160fc114598df514e8/transformed/constraintlayout-2.0.1/res
com.anonymous.myapp-cardview-1.0.0-18 /Users/<USER>/.gradle/caches/8.13/transforms/57b0ed4888c5d35f21d94e7e07d01862/transformed/cardview-1.0.0/res
com.anonymous.myapp-autofill-1.1.0-19 /Users/<USER>/.gradle/caches/8.13/transforms/6126635189ee6e9915c55ed159ffcefd/transformed/autofill-1.1.0/res
com.anonymous.myapp-coordinatorlayout-1.2.0-20 /Users/<USER>/.gradle/caches/8.13/transforms/675643d2417f53f62de6e71cb70c4a03/transformed/coordinatorlayout-1.2.0/res
com.anonymous.myapp-BlurView-version-2.0.6-21 /Users/<USER>/.gradle/caches/8.13/transforms/67a76227458514088161a2b0747e4617/transformed/BlurView-version-2.0.6/res
com.anonymous.myapp-lifecycle-viewmodel-ktx-2.6.2-22 /Users/<USER>/.gradle/caches/8.13/transforms/6a7fec56ee66dd7f7a0a2b21f81fa72c/transformed/lifecycle-viewmodel-ktx-2.6.2/res
com.anonymous.myapp-glide-4.16.0-23 /Users/<USER>/.gradle/caches/8.13/transforms/6d4036466a3da156a07d7c15151d5f6a/transformed/glide-4.16.0/res
com.anonymous.myapp-lifecycle-livedata-core-2.6.2-24 /Users/<USER>/.gradle/caches/8.13/transforms/74c5ef7ab2dc463f805db9e9370b6b3a/transformed/lifecycle-livedata-core-2.6.2/res
com.anonymous.myapp-frameanimation-3.0.3-25 /Users/<USER>/.gradle/caches/8.13/transforms/76f79e0c31791936fb2bd92707f6357d/transformed/frameanimation-3.0.3/res
com.anonymous.myapp-fragment-1.6.1-26 /Users/<USER>/.gradle/caches/8.13/transforms/7cf11ab6a97ce7a19d873c7fc9f6245f/transformed/fragment-1.6.1/res
com.anonymous.myapp-activity-ktx-1.8.0-27 /Users/<USER>/.gradle/caches/8.13/transforms/7e03b29ff010dcda13836cde37647491/transformed/activity-ktx-1.8.0/res
com.anonymous.myapp-awebp-3.0.3-28 /Users/<USER>/.gradle/caches/8.13/transforms/7ee6ee78b8b7385b6567f6e3572e56c2/transformed/awebp-3.0.3/res
com.anonymous.myapp-emoji2-views-helper-1.3.0-29 /Users/<USER>/.gradle/caches/8.13/transforms/8109800e0431b448d21295476395ef1d/transformed/emoji2-views-helper-1.3.0/res
com.anonymous.myapp-savedstate-ktx-1.2.1-30 /Users/<USER>/.gradle/caches/8.13/transforms/86cbd6534acf09b6edb34f1ee4cf1ea6/transformed/savedstate-ktx-1.2.1/res
com.anonymous.myapp-activity-1.8.0-31 /Users/<USER>/.gradle/caches/8.13/transforms/8f54346d7887f87b0ac14941a641e1e5/transformed/activity-1.8.0/res
com.anonymous.myapp-annotation-experimental-1.4.0-32 /Users/<USER>/.gradle/caches/8.13/transforms/9a3668def9ff4e9f17f553dcb425dac8/transformed/annotation-experimental-1.4.0/res
com.anonymous.myapp-swiperefreshlayout-1.1.0-33 /Users/<USER>/.gradle/caches/8.13/transforms/a803a10f06d03b85c791e9e7827df3a7/transformed/swiperefreshlayout-1.1.0/res
com.anonymous.myapp-startup-runtime-1.1.1-34 /Users/<USER>/.gradle/caches/8.13/transforms/b02b404f32dd5de34e82e545d50d8192/transformed/startup-runtime-1.1.1/res
com.anonymous.myapp-expo.modules.splashscreen-0.30.9-35 /Users/<USER>/.gradle/caches/8.13/transforms/b3390bd7dd069ce47ea2215a9701dc04/transformed/expo.modules.splashscreen-0.30.9/res
com.anonymous.myapp-lifecycle-viewmodel-savedstate-2.6.2-36 /Users/<USER>/.gradle/caches/8.13/transforms/b6d7366c2eeb6fe782cd6baab9130943/transformed/lifecycle-viewmodel-savedstate-2.6.2/res
com.anonymous.myapp-lifecycle-viewmodel-2.6.2-37 /Users/<USER>/.gradle/caches/8.13/transforms/bae0062137d08dab4b3c13dc4624a335/transformed/lifecycle-viewmodel-2.6.2/res
com.anonymous.myapp-transition-1.5.0-38 /Users/<USER>/.gradle/caches/8.13/transforms/bdbe8332f585138dd54b686bb64a82aa/transformed/transition-1.5.0/res
com.anonymous.myapp-emoji2-1.3.0-39 /Users/<USER>/.gradle/caches/8.13/transforms/bdcbec29e8d0b717843f3dbae0b94f84/transformed/emoji2-1.3.0/res
com.anonymous.myapp-lifecycle-livedata-2.6.2-40 /Users/<USER>/.gradle/caches/8.13/transforms/c0f61044d9766c534145df491b529aab/transformed/lifecycle-livedata-2.6.2/res
com.anonymous.myapp-apng-3.0.3-41 /Users/<USER>/.gradle/caches/8.13/transforms/e02d6619476aba0105310be2159d96f9/transformed/apng-3.0.3/res
com.anonymous.myapp-core-splashscreen-1.2.0-alpha02-42 /Users/<USER>/.gradle/caches/8.13/transforms/e03d0108970ad49120068551e56f0044/transformed/core-splashscreen-1.2.0-alpha02/res
com.anonymous.myapp-gif-3.0.3-43 /Users/<USER>/.gradle/caches/8.13/transforms/ea85dbefea5c1958d4cdd80a099d89cf/transformed/gif-3.0.3/res
com.anonymous.myapp-drawerlayout-1.1.1-44 /Users/<USER>/.gradle/caches/8.13/transforms/eabbbcce580f93f46e8da890fc2dc6b7/transformed/drawerlayout-1.1.1/res
com.anonymous.myapp-tracing-1.2.0-45 /Users/<USER>/.gradle/caches/8.13/transforms/eb45ff10c0db144c5a5537429e93efe3/transformed/tracing-1.2.0/res
com.anonymous.myapp-lifecycle-process-2.6.2-46 /Users/<USER>/.gradle/caches/8.13/transforms/ec3f62d4745054bc29342c5607bcde4d/transformed/lifecycle-process-2.6.2/res
com.anonymous.myapp-react-android-0.79.4-debug-47 /Users/<USER>/.gradle/caches/8.13/transforms/ee3752888ecf2d82fe4920d23f93a394/transformed/react-android-0.79.4-debug/res
com.anonymous.myapp-glide-plugin-3.0.3-48 /Users/<USER>/.gradle/caches/8.13/transforms/f40077589a7ac8623ba297258e75d28b/transformed/glide-plugin-3.0.3/res
com.anonymous.myapp-avif-3.0.3-49 /Users/<USER>/.gradle/caches/8.13/transforms/fa93cb06fb117b034e9e00e2254e875d/transformed/avif-3.0.3/res
com.anonymous.myapp-lifecycle-runtime-2.6.2-50 /Users/<USER>/.gradle/caches/8.13/transforms/fb72dc500fcacad8fdd089fb6045d7e1/transformed/lifecycle-runtime-2.6.2/res
com.anonymous.myapp-core-runtime-2.2.0-51 /Users/<USER>/.gradle/caches/8.13/transforms/fcdbde368ff3f7b130f131373693d72e/transformed/core-runtime-2.2.0/res
com.anonymous.myapp-lifecycle-runtime-ktx-2.6.2-52 /Users/<USER>/.gradle/caches/8.13/transforms/fece13e9c08c13338369de69cd14e88c/transformed/lifecycle-runtime-ktx-2.6.2/res
com.anonymous.myapp-pngs-53 /Users/<USER>/not_work/my-app/android/app/build/generated/res/pngs/debug
com.anonymous.myapp-resValues-54 /Users/<USER>/not_work/my-app/android/app/build/generated/res/resValues/debug
com.anonymous.myapp-packageDebugResources-55 /Users/<USER>/not_work/my-app/android/app/build/intermediates/incremental/debug/packageDebugResources/merged.dir
com.anonymous.myapp-packageDebugResources-56 /Users/<USER>/not_work/my-app/android/app/build/intermediates/incremental/debug/packageDebugResources/stripped.dir
com.anonymous.myapp-debug-57 /Users/<USER>/not_work/my-app/android/app/build/intermediates/merged_res/debug/mergeDebugResources
com.anonymous.myapp-debug-58 /Users/<USER>/not_work/my-app/android/app/src/debug/res
com.anonymous.myapp-main-59 /Users/<USER>/not_work/my-app/android/app/src/main/res
com.anonymous.myapp-debug-60 /Users/<USER>/not_work/my-app/node_modules/expo-constants/android/build/intermediates/packaged_res/debug/packageDebugResources
com.anonymous.myapp-debug-61 /Users/<USER>/not_work/my-app/node_modules/expo-file-system/android/build/intermediates/packaged_res/debug/packageDebugResources
com.anonymous.myapp-debug-62 /Users/<USER>/not_work/my-app/node_modules/expo-linking/android/build/intermediates/packaged_res/debug/packageDebugResources
com.anonymous.myapp-debug-63 /Users/<USER>/not_work/my-app/node_modules/expo-modules-core/android/build/intermediates/packaged_res/debug/packageDebugResources
com.anonymous.myapp-debug-64 /Users/<USER>/not_work/my-app/node_modules/expo/android/build/intermediates/packaged_res/debug/packageDebugResources
com.anonymous.myapp-debug-65 /Users/<USER>/not_work/my-app/node_modules/react-native-edge-to-edge/android/build/intermediates/packaged_res/debug/packageDebugResources
com.anonymous.myapp-debug-66 /Users/<USER>/not_work/my-app/node_modules/react-native-gesture-handler/android/build/intermediates/packaged_res/debug/packageDebugResources
com.anonymous.myapp-debug-67 /Users/<USER>/not_work/my-app/node_modules/react-native-reanimated/android/build/intermediates/packaged_res/debug/packageDebugResources
com.anonymous.myapp-debug-68 /Users/<USER>/not_work/my-app/node_modules/react-native-safe-area-context/android/build/intermediates/packaged_res/debug/packageDebugResources
com.anonymous.myapp-debug-69 /Users/<USER>/not_work/my-app/node_modules/react-native-screens/android/build/intermediates/packaged_res/debug/packageDebugResources
com.anonymous.myapp-debug-70 /Users/<USER>/not_work/my-app/node_modules/react-native-webview/android/build/intermediates/packaged_res/debug/packageDebugResources
