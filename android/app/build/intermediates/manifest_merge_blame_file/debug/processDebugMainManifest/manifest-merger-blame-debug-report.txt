1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.anonymous.myapp"
4    android:versionCode="1"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="35" />
10
11    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
11-->/Users/<USER>/not_work/my-app/android/app/src/main/AndroidManifest.xml:4:3-75
11-->/Users/<USER>/not_work/my-app/android/app/src/main/AndroidManifest.xml:4:20-73
12    <uses-permission android:name="android.permission.INTERNET" />
12-->/Users/<USER>/not_work/my-app/android/app/src/main/AndroidManifest.xml:2:3-64
12-->/Users/<USER>/not_work/my-app/android/app/src/main/AndroidManifest.xml:2:20-62
13    <uses-permission
13-->/Users/<USER>/not_work/my-app/android/app/src/main/AndroidManifest.xml:3:3-77
14        android:name="android.permission.READ_EXTERNAL_STORAGE"
14-->/Users/<USER>/not_work/my-app/android/app/src/main/AndroidManifest.xml:3:20-75
15        android:maxSdkVersion="32" />
15-->[BareExpo:expo.modules.image:2.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/3436d2bb0e66cba9430428ecbc7fb171/transformed/expo.modules.image-2.3.0/AndroidManifest.xml:17:9-35
16    <uses-permission android:name="android.permission.VIBRATE" />
16-->/Users/<USER>/not_work/my-app/android/app/src/main/AndroidManifest.xml:5:3-63
16-->/Users/<USER>/not_work/my-app/android/app/src/main/AndroidManifest.xml:5:20-61
17    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
17-->/Users/<USER>/not_work/my-app/android/app/src/main/AndroidManifest.xml:6:3-78
17-->/Users/<USER>/not_work/my-app/android/app/src/main/AndroidManifest.xml:6:20-76
18
19    <queries>
19-->/Users/<USER>/not_work/my-app/android/app/src/main/AndroidManifest.xml:7:3-13:13
20        <intent>
20-->/Users/<USER>/not_work/my-app/android/app/src/main/AndroidManifest.xml:8:5-12:14
21            <action android:name="android.intent.action.VIEW" />
21-->/Users/<USER>/not_work/my-app/android/app/src/main/AndroidManifest.xml:9:7-58
21-->/Users/<USER>/not_work/my-app/android/app/src/main/AndroidManifest.xml:9:15-56
22
23            <category android:name="android.intent.category.BROWSABLE" />
23-->/Users/<USER>/not_work/my-app/android/app/src/main/AndroidManifest.xml:10:7-67
23-->/Users/<USER>/not_work/my-app/android/app/src/main/AndroidManifest.xml:10:17-65
24
25            <data android:scheme="https" />
25-->/Users/<USER>/not_work/my-app/android/app/src/main/AndroidManifest.xml:11:7-37
25-->/Users/<USER>/not_work/my-app/android/app/src/main/AndroidManifest.xml:11:13-35
26        </intent>
27        <!-- Query open documents -->
28        <intent>
28-->[:expo-file-system] /Users/<USER>/not_work/my-app/node_modules/expo-file-system/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:15:9-17:18
29            <action android:name="android.intent.action.OPEN_DOCUMENT_TREE" />
29-->[:expo-file-system] /Users/<USER>/not_work/my-app/node_modules/expo-file-system/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:16:13-79
29-->[:expo-file-system] /Users/<USER>/not_work/my-app/node_modules/expo-file-system/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:16:21-76
30        </intent>
31        <intent>
31-->[host.exp.exponent:expo.modules.webbrowser:14.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/b2e62f529b3dc5334aec2c40acace88a/transformed/expo.modules.webbrowser-14.2.0/AndroidManifest.xml:8:9-12:18
32
33            <!-- Required for opening tabs if targeting API 30 -->
34            <action android:name="android.support.customtabs.action.CustomTabsService" />
34-->[host.exp.exponent:expo.modules.webbrowser:14.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/b2e62f529b3dc5334aec2c40acace88a/transformed/expo.modules.webbrowser-14.2.0/AndroidManifest.xml:11:13-90
34-->[host.exp.exponent:expo.modules.webbrowser:14.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/b2e62f529b3dc5334aec2c40acace88a/transformed/expo.modules.webbrowser-14.2.0/AndroidManifest.xml:11:21-87
35        </intent>
36    </queries>
37    <!--
38  Allows Glide to monitor connectivity status and restart failed requests if users go from a
39  a disconnected to a connected network state.
40    -->
41    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
41-->[BareExpo:expo.modules.image:2.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/3436d2bb0e66cba9430428ecbc7fb171/transformed/expo.modules.image-2.3.0/AndroidManifest.xml:12:5-79
41-->[BareExpo:expo.modules.image:2.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/3436d2bb0e66cba9430428ecbc7fb171/transformed/expo.modules.image-2.3.0/AndroidManifest.xml:12:22-76
42
43    <permission
43-->[androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.13/transforms/25ad4bdeddd79d498a0e9dfe4af5a7d2/transformed/core-1.13.1/AndroidManifest.xml:22:5-24:47
44        android:name="com.anonymous.myapp.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
44-->[androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.13/transforms/25ad4bdeddd79d498a0e9dfe4af5a7d2/transformed/core-1.13.1/AndroidManifest.xml:23:9-81
45        android:protectionLevel="signature" />
45-->[androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.13/transforms/25ad4bdeddd79d498a0e9dfe4af5a7d2/transformed/core-1.13.1/AndroidManifest.xml:24:9-44
46
47    <uses-permission android:name="com.anonymous.myapp.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
47-->[androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.13/transforms/25ad4bdeddd79d498a0e9dfe4af5a7d2/transformed/core-1.13.1/AndroidManifest.xml:26:5-97
47-->[androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.13/transforms/25ad4bdeddd79d498a0e9dfe4af5a7d2/transformed/core-1.13.1/AndroidManifest.xml:26:22-94
48
49    <application
49-->/Users/<USER>/not_work/my-app/android/app/src/main/AndroidManifest.xml:14:3-30:17
50        android:name="com.anonymous.myapp.MainApplication"
50-->/Users/<USER>/not_work/my-app/android/app/src/main/AndroidManifest.xml:14:16-47
51        android:allowBackup="true"
51-->/Users/<USER>/not_work/my-app/android/app/src/main/AndroidManifest.xml:14:162-188
52        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
52-->[androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.13/transforms/25ad4bdeddd79d498a0e9dfe4af5a7d2/transformed/core-1.13.1/AndroidManifest.xml:28:18-86
53        android:debuggable="true"
54        android:extractNativeLibs="false"
55        android:icon="@mipmap/ic_launcher"
55-->/Users/<USER>/not_work/my-app/android/app/src/main/AndroidManifest.xml:14:81-115
56        android:label="@string/app_name"
56-->/Users/<USER>/not_work/my-app/android/app/src/main/AndroidManifest.xml:14:48-80
57        android:roundIcon="@mipmap/ic_launcher_round"
57-->/Users/<USER>/not_work/my-app/android/app/src/main/AndroidManifest.xml:14:116-161
58        android:supportsRtl="true"
58-->/Users/<USER>/not_work/my-app/android/app/src/main/AndroidManifest.xml:14:221-247
59        android:theme="@style/AppTheme"
59-->/Users/<USER>/not_work/my-app/android/app/src/main/AndroidManifest.xml:14:189-220
60        android:usesCleartextTraffic="true" >
60-->/Users/<USER>/not_work/my-app/android/app/src/debug/AndroidManifest.xml:6:18-53
61        <meta-data
61-->/Users/<USER>/not_work/my-app/android/app/src/main/AndroidManifest.xml:15:5-83
62            android:name="expo.modules.updates.ENABLED"
62-->/Users/<USER>/not_work/my-app/android/app/src/main/AndroidManifest.xml:15:16-59
63            android:value="false" />
63-->/Users/<USER>/not_work/my-app/android/app/src/main/AndroidManifest.xml:15:60-81
64        <meta-data
64-->/Users/<USER>/not_work/my-app/android/app/src/main/AndroidManifest.xml:16:5-105
65            android:name="expo.modules.updates.EXPO_UPDATES_CHECK_ON_LAUNCH"
65-->/Users/<USER>/not_work/my-app/android/app/src/main/AndroidManifest.xml:16:16-80
66            android:value="ALWAYS" />
66-->/Users/<USER>/not_work/my-app/android/app/src/main/AndroidManifest.xml:16:81-103
67        <meta-data
67-->/Users/<USER>/not_work/my-app/android/app/src/main/AndroidManifest.xml:17:5-99
68            android:name="expo.modules.updates.EXPO_UPDATES_LAUNCH_WAIT_MS"
68-->/Users/<USER>/not_work/my-app/android/app/src/main/AndroidManifest.xml:17:16-79
69            android:value="0" />
69-->/Users/<USER>/not_work/my-app/android/app/src/main/AndroidManifest.xml:17:80-97
70
71        <activity
71-->/Users/<USER>/not_work/my-app/android/app/src/main/AndroidManifest.xml:18:5-29:16
72            android:name="com.anonymous.myapp.MainActivity"
72-->/Users/<USER>/not_work/my-app/android/app/src/main/AndroidManifest.xml:18:15-43
73            android:configChanges="keyboard|keyboardHidden|orientation|screenSize|screenLayout|uiMode"
73-->/Users/<USER>/not_work/my-app/android/app/src/main/AndroidManifest.xml:18:44-134
74            android:exported="true"
74-->/Users/<USER>/not_work/my-app/android/app/src/main/AndroidManifest.xml:18:256-279
75            android:launchMode="singleTask"
75-->/Users/<USER>/not_work/my-app/android/app/src/main/AndroidManifest.xml:18:135-166
76            android:screenOrientation="portrait"
76-->/Users/<USER>/not_work/my-app/android/app/src/main/AndroidManifest.xml:18:280-316
77            android:theme="@style/Theme.App.SplashScreen"
77-->/Users/<USER>/not_work/my-app/android/app/src/main/AndroidManifest.xml:18:210-255
78            android:windowSoftInputMode="adjustResize" >
78-->/Users/<USER>/not_work/my-app/android/app/src/main/AndroidManifest.xml:18:167-209
79            <intent-filter>
79-->/Users/<USER>/not_work/my-app/android/app/src/main/AndroidManifest.xml:19:7-22:23
80                <action android:name="android.intent.action.MAIN" />
80-->/Users/<USER>/not_work/my-app/android/app/src/main/AndroidManifest.xml:20:9-60
80-->/Users/<USER>/not_work/my-app/android/app/src/main/AndroidManifest.xml:20:17-58
81
82                <category android:name="android.intent.category.LAUNCHER" />
82-->/Users/<USER>/not_work/my-app/android/app/src/main/AndroidManifest.xml:21:9-68
82-->/Users/<USER>/not_work/my-app/android/app/src/main/AndroidManifest.xml:21:19-66
83            </intent-filter>
84            <intent-filter>
84-->/Users/<USER>/not_work/my-app/android/app/src/main/AndroidManifest.xml:23:7-28:23
85                <action android:name="android.intent.action.VIEW" />
85-->/Users/<USER>/not_work/my-app/android/app/src/main/AndroidManifest.xml:9:7-58
85-->/Users/<USER>/not_work/my-app/android/app/src/main/AndroidManifest.xml:9:15-56
86
87                <category android:name="android.intent.category.DEFAULT" />
87-->/Users/<USER>/not_work/my-app/android/app/src/main/AndroidManifest.xml:25:9-67
87-->/Users/<USER>/not_work/my-app/android/app/src/main/AndroidManifest.xml:25:19-65
88                <category android:name="android.intent.category.BROWSABLE" />
88-->/Users/<USER>/not_work/my-app/android/app/src/main/AndroidManifest.xml:10:7-67
88-->/Users/<USER>/not_work/my-app/android/app/src/main/AndroidManifest.xml:10:17-65
89
90                <data android:scheme="myapp" />
90-->/Users/<USER>/not_work/my-app/android/app/src/main/AndroidManifest.xml:11:7-37
90-->/Users/<USER>/not_work/my-app/android/app/src/main/AndroidManifest.xml:11:13-35
91            </intent-filter>
92        </activity>
93
94        <provider
94-->[:react-native-webview] /Users/<USER>/not_work/my-app/node_modules/react-native-webview/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:8:9-16:20
95            android:name="com.reactnativecommunity.webview.RNCWebViewFileProvider"
95-->[:react-native-webview] /Users/<USER>/not_work/my-app/node_modules/react-native-webview/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:9:13-83
96            android:authorities="com.anonymous.myapp.fileprovider"
96-->[:react-native-webview] /Users/<USER>/not_work/my-app/node_modules/react-native-webview/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:10:13-64
97            android:exported="false"
97-->[:react-native-webview] /Users/<USER>/not_work/my-app/node_modules/react-native-webview/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:11:13-37
98            android:grantUriPermissions="true" >
98-->[:react-native-webview] /Users/<USER>/not_work/my-app/node_modules/react-native-webview/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:12:13-47
99            <meta-data
99-->[:react-native-webview] /Users/<USER>/not_work/my-app/node_modules/react-native-webview/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:13:13-15:63
100                android:name="android.support.FILE_PROVIDER_PATHS"
100-->[:react-native-webview] /Users/<USER>/not_work/my-app/node_modules/react-native-webview/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:14:17-67
101                android:resource="@xml/file_provider_paths" />
101-->[:react-native-webview] /Users/<USER>/not_work/my-app/node_modules/react-native-webview/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:15:17-60
102        </provider>
103
104        <meta-data
104-->[:expo-modules-core] /Users/<USER>/not_work/my-app/node_modules/expo-modules-core/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:9:9-11:89
105            android:name="org.unimodules.core.AppLoader#react-native-headless"
105-->[:expo-modules-core] /Users/<USER>/not_work/my-app/node_modules/expo-modules-core/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:10:13-79
106            android:value="expo.modules.adapters.react.apploader.RNHeadlessAppLoader" />
106-->[:expo-modules-core] /Users/<USER>/not_work/my-app/node_modules/expo-modules-core/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:11:13-86
107        <meta-data
107-->[:expo-modules-core] /Users/<USER>/not_work/my-app/node_modules/expo-modules-core/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:12:9-15:45
108            android:name="com.facebook.soloader.enabled"
108-->[:expo-modules-core] /Users/<USER>/not_work/my-app/node_modules/expo-modules-core/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:13:13-57
109            android:value="true" />
109-->[:expo-modules-core] /Users/<USER>/not_work/my-app/node_modules/expo-modules-core/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:14:13-33
110
111        <activity
111-->[com.facebook.react:react-android:0.79.4] /Users/<USER>/.gradle/caches/8.13/transforms/ee3752888ecf2d82fe4920d23f93a394/transformed/react-android-0.79.4-debug/AndroidManifest.xml:19:9-21:40
112            android:name="com.facebook.react.devsupport.DevSettingsActivity"
112-->[com.facebook.react:react-android:0.79.4] /Users/<USER>/.gradle/caches/8.13/transforms/ee3752888ecf2d82fe4920d23f93a394/transformed/react-android-0.79.4-debug/AndroidManifest.xml:20:13-77
113            android:exported="false" />
113-->[com.facebook.react:react-android:0.79.4] /Users/<USER>/.gradle/caches/8.13/transforms/ee3752888ecf2d82fe4920d23f93a394/transformed/react-android-0.79.4-debug/AndroidManifest.xml:21:13-37
114
115        <provider
115-->[:expo-file-system] /Users/<USER>/not_work/my-app/node_modules/expo-file-system/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:21:9-30:20
116            android:name="expo.modules.filesystem.FileSystemFileProvider"
116-->[:expo-file-system] /Users/<USER>/not_work/my-app/node_modules/expo-file-system/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:22:13-74
117            android:authorities="com.anonymous.myapp.FileSystemFileProvider"
117-->[:expo-file-system] /Users/<USER>/not_work/my-app/node_modules/expo-file-system/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:23:13-74
118            android:exported="false"
118-->[:expo-file-system] /Users/<USER>/not_work/my-app/node_modules/expo-file-system/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:24:13-37
119            android:grantUriPermissions="true" >
119-->[:expo-file-system] /Users/<USER>/not_work/my-app/node_modules/expo-file-system/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:25:13-47
120            <meta-data
120-->[:react-native-webview] /Users/<USER>/not_work/my-app/node_modules/react-native-webview/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:13:13-15:63
121                android:name="android.support.FILE_PROVIDER_PATHS"
121-->[:react-native-webview] /Users/<USER>/not_work/my-app/node_modules/react-native-webview/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:14:17-67
122                android:resource="@xml/file_system_provider_paths" />
122-->[:react-native-webview] /Users/<USER>/not_work/my-app/node_modules/react-native-webview/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:15:17-60
123        </provider>
124
125        <meta-data
125-->[com.github.bumptech.glide:okhttp3-integration:4.11.0] /Users/<USER>/.gradle/caches/8.13/transforms/20c2c99566bca8247160f1fa1ef8c1cc/transformed/okhttp3-integration-4.11.0/AndroidManifest.xml:11:9-13:43
126            android:name="com.bumptech.glide.integration.okhttp3.OkHttpGlideModule"
126-->[com.github.bumptech.glide:okhttp3-integration:4.11.0] /Users/<USER>/.gradle/caches/8.13/transforms/20c2c99566bca8247160f1fa1ef8c1cc/transformed/okhttp3-integration-4.11.0/AndroidManifest.xml:12:13-84
127            android:value="GlideModule" />
127-->[com.github.bumptech.glide:okhttp3-integration:4.11.0] /Users/<USER>/.gradle/caches/8.13/transforms/20c2c99566bca8247160f1fa1ef8c1cc/transformed/okhttp3-integration-4.11.0/AndroidManifest.xml:13:13-40
128
129        <provider
129-->[androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/bdcbec29e8d0b717843f3dbae0b94f84/transformed/emoji2-1.3.0/AndroidManifest.xml:24:9-32:20
130            android:name="androidx.startup.InitializationProvider"
130-->[androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/bdcbec29e8d0b717843f3dbae0b94f84/transformed/emoji2-1.3.0/AndroidManifest.xml:25:13-67
131            android:authorities="com.anonymous.myapp.androidx-startup"
131-->[androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/bdcbec29e8d0b717843f3dbae0b94f84/transformed/emoji2-1.3.0/AndroidManifest.xml:26:13-68
132            android:exported="false" >
132-->[androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/bdcbec29e8d0b717843f3dbae0b94f84/transformed/emoji2-1.3.0/AndroidManifest.xml:27:13-37
133            <meta-data
133-->[androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/bdcbec29e8d0b717843f3dbae0b94f84/transformed/emoji2-1.3.0/AndroidManifest.xml:29:13-31:52
134                android:name="androidx.emoji2.text.EmojiCompatInitializer"
134-->[androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/bdcbec29e8d0b717843f3dbae0b94f84/transformed/emoji2-1.3.0/AndroidManifest.xml:30:17-75
135                android:value="androidx.startup" />
135-->[androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/bdcbec29e8d0b717843f3dbae0b94f84/transformed/emoji2-1.3.0/AndroidManifest.xml:31:17-49
136            <meta-data
136-->[androidx.lifecycle:lifecycle-process:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/ec3f62d4745054bc29342c5607bcde4d/transformed/lifecycle-process-2.6.2/AndroidManifest.xml:29:13-31:52
137                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
137-->[androidx.lifecycle:lifecycle-process:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/ec3f62d4745054bc29342c5607bcde4d/transformed/lifecycle-process-2.6.2/AndroidManifest.xml:30:17-78
138                android:value="androidx.startup" />
138-->[androidx.lifecycle:lifecycle-process:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/ec3f62d4745054bc29342c5607bcde4d/transformed/lifecycle-process-2.6.2/AndroidManifest.xml:31:17-49
139            <meta-data
139-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/3070f604e3c80036d56e6e100694a483/transformed/profileinstaller-1.3.1/AndroidManifest.xml:29:13-31:52
140                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
140-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/3070f604e3c80036d56e6e100694a483/transformed/profileinstaller-1.3.1/AndroidManifest.xml:30:17-85
141                android:value="androidx.startup" />
141-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/3070f604e3c80036d56e6e100694a483/transformed/profileinstaller-1.3.1/AndroidManifest.xml:31:17-49
142        </provider>
143
144        <receiver
144-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/3070f604e3c80036d56e6e100694a483/transformed/profileinstaller-1.3.1/AndroidManifest.xml:34:9-52:20
145            android:name="androidx.profileinstaller.ProfileInstallReceiver"
145-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/3070f604e3c80036d56e6e100694a483/transformed/profileinstaller-1.3.1/AndroidManifest.xml:35:13-76
146            android:directBootAware="false"
146-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/3070f604e3c80036d56e6e100694a483/transformed/profileinstaller-1.3.1/AndroidManifest.xml:36:13-44
147            android:enabled="true"
147-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/3070f604e3c80036d56e6e100694a483/transformed/profileinstaller-1.3.1/AndroidManifest.xml:37:13-35
148            android:exported="true"
148-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/3070f604e3c80036d56e6e100694a483/transformed/profileinstaller-1.3.1/AndroidManifest.xml:38:13-36
149            android:permission="android.permission.DUMP" >
149-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/3070f604e3c80036d56e6e100694a483/transformed/profileinstaller-1.3.1/AndroidManifest.xml:39:13-57
150            <intent-filter>
150-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/3070f604e3c80036d56e6e100694a483/transformed/profileinstaller-1.3.1/AndroidManifest.xml:40:13-42:29
151                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
151-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/3070f604e3c80036d56e6e100694a483/transformed/profileinstaller-1.3.1/AndroidManifest.xml:41:17-91
151-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/3070f604e3c80036d56e6e100694a483/transformed/profileinstaller-1.3.1/AndroidManifest.xml:41:25-88
152            </intent-filter>
153            <intent-filter>
153-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/3070f604e3c80036d56e6e100694a483/transformed/profileinstaller-1.3.1/AndroidManifest.xml:43:13-45:29
154                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
154-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/3070f604e3c80036d56e6e100694a483/transformed/profileinstaller-1.3.1/AndroidManifest.xml:44:17-85
154-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/3070f604e3c80036d56e6e100694a483/transformed/profileinstaller-1.3.1/AndroidManifest.xml:44:25-82
155            </intent-filter>
156            <intent-filter>
156-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/3070f604e3c80036d56e6e100694a483/transformed/profileinstaller-1.3.1/AndroidManifest.xml:46:13-48:29
157                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
157-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/3070f604e3c80036d56e6e100694a483/transformed/profileinstaller-1.3.1/AndroidManifest.xml:47:17-88
157-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/3070f604e3c80036d56e6e100694a483/transformed/profileinstaller-1.3.1/AndroidManifest.xml:47:25-85
158            </intent-filter>
159            <intent-filter>
159-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/3070f604e3c80036d56e6e100694a483/transformed/profileinstaller-1.3.1/AndroidManifest.xml:49:13-51:29
160                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
160-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/3070f604e3c80036d56e6e100694a483/transformed/profileinstaller-1.3.1/AndroidManifest.xml:50:17-95
160-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/3070f604e3c80036d56e6e100694a483/transformed/profileinstaller-1.3.1/AndroidManifest.xml:50:25-92
161            </intent-filter>
162        </receiver>
163    </application>
164
165</manifest>
