-- Merging decision tree log ---
manifest
ADDED from /Users/<USER>/not_work/my-app/android/app/src/main/AndroidManifest.xml:1:1-31:12
MERGED from /Users/<USER>/not_work/my-app/android/app/src/main/AndroidManifest.xml:1:1-31:12
INJECTED from /Users/<USER>/not_work/my-app/android/app/src/debug/AndroidManifest.xml:1:1-7:12
INJECTED from /Users/<USER>/not_work/my-app/android/app/src/debug/AndroidManifest.xml:1:1-7:12
INJECTED from /Users/<USER>/not_work/my-app/android/app/src/debug/AndroidManifest.xml:1:1-7:12
MERGED from [:react-native-gesture-handler] /Users/<USER>/not_work/my-app/node_modules/react-native-gesture-handler/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-safe-area-context] /Users/<USER>/not_work/my-app/node_modules/react-native-safe-area-context/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-screens] /Users/<USER>/not_work/my-app/node_modules/react-native-screens/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-webview] /Users/<USER>/not_work/my-app/node_modules/react-native-webview/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-19:12
MERGED from [:react-native-edge-to-edge] /Users/<USER>/not_work/my-app/node_modules/react-native-edge-to-edge/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-7:12
MERGED from [:expo] /Users/<USER>/not_work/my-app/node_modules/expo/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-reanimated] /Users/<USER>/not_work/my-app/node_modules/react-native-reanimated/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-7:12
MERGED from [:expo-modules-core] /Users/<USER>/not_work/my-app/node_modules/expo-modules-core/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-18:12
MERGED from [host.exp.exponent:expo.modules.font:13.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/d97e291462cdacba71e8ea9143529a40/transformed/expo.modules.font-13.3.1/AndroidManifest.xml:2:1-7:12
MERGED from [BareExpo:expo.modules.image:2.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/3436d2bb0e66cba9430428ecbc7fb171/transformed/expo.modules.image-2.3.0/AndroidManifest.xml:2:1-19:12
MERGED from [host.exp.exponent:expo.modules.splashscreen:0.30.9] /Users/<USER>/.gradle/caches/8.13/transforms/b3390bd7dd069ce47ea2215a9701dc04/transformed/expo.modules.splashscreen-0.30.9/AndroidManifest.xml:2:1-7:12
MERGED from [host.exp.exponent:expo.modules.systemui:5.0.9] /Users/<USER>/.gradle/caches/8.13/transforms/242ac0c339cf89d88a6b948ccf19dfd1/transformed/expo.modules.systemui-5.0.9/AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.react:react-android:0.79.4] /Users/<USER>/.gradle/caches/8.13/transforms/ee3752888ecf2d82fe4920d23f93a394/transformed/react-android-0.79.4-debug/AndroidManifest.xml:2:1-24:12
MERGED from [:expo-constants] /Users/<USER>/not_work/my-app/node_modules/expo-constants/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-7:12
MERGED from [:expo-file-system] /Users/<USER>/not_work/my-app/node_modules/expo-file-system/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-33:12
MERGED from [:expo-linking] /Users/<USER>/not_work/my-app/node_modules/expo-linking/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-7:12
MERGED from [expo.modules.asset:expo.modules.asset:11.1.5] /Users/<USER>/.gradle/caches/8.13/transforms/e8a3e9b5e96af02342a176ccdc544ece/transformed/expo.modules.asset-11.1.5/AndroidManifest.xml:2:1-7:12
MERGED from [host.exp.exponent:expo.modules.blur:14.1.5] /Users/<USER>/.gradle/caches/8.13/transforms/6de1916b90041fd9e517ff96a11caa6d/transformed/expo.modules.blur-14.1.5/AndroidManifest.xml:2:1-7:12
MERGED from [host.exp.exponent:expo.modules.haptics:14.1.4] /Users/<USER>/.gradle/caches/8.13/transforms/45fbacacfd03779f88e5bdab927202e7/transformed/expo.modules.haptics-14.1.4/AndroidManifest.xml:2:1-9:12
MERGED from [host.exp.exponent:expo.modules.keepawake:14.1.4] /Users/<USER>/.gradle/caches/8.13/transforms/9e4cfa109c9b9afbaf07c5dc128efb23/transformed/expo.modules.keepawake-14.1.4/AndroidManifest.xml:2:1-7:12
MERGED from [host.exp.exponent:expo.modules.webbrowser:14.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/b2e62f529b3dc5334aec2c40acace88a/transformed/expo.modules.webbrowser-14.2.0/AndroidManifest.xml:2:1-15:12
MERGED from [com.google.android.material:material:1.12.0] /Users/<USER>/.gradle/caches/8.13/transforms/1688e4f4998421f6cdbc59f17cef2f61/transformed/material-1.12.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.core:core-splashscreen:1.2.0-alpha02] /Users/<USER>/.gradle/caches/8.13/transforms/e03d0108970ad49120068551e56f0044/transformed/core-splashscreen-1.2.0-alpha02/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/0ea8215bc09ef74c628f4e4a0402513e/transformed/appcompat-resources-1.7.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.github.penfeizhou.android.animation:glide-plugin:3.0.3] /Users/<USER>/.gradle/caches/8.13/transforms/f40077589a7ac8623ba297258e75d28b/transformed/glide-plugin-3.0.3/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] /Users/<USER>/.gradle/caches/8.13/transforms/4a6ff7a886435c160fc114598df514e8/transformed/constraintlayout-2.0.1/AndroidManifest.xml:2:1-11:12
MERGED from [com.github.penfeizhou.android.animation:awebp:3.0.3] /Users/<USER>/.gradle/caches/8.13/transforms/7ee6ee78b8b7385b6567f6e3572e56c2/transformed/awebp-3.0.3/AndroidManifest.xml:2:1-7:12
MERGED from [com.github.penfeizhou.android.animation:apng:3.0.3] /Users/<USER>/.gradle/caches/8.13/transforms/e02d6619476aba0105310be2159d96f9/transformed/apng-3.0.3/AndroidManifest.xml:2:1-7:12
MERGED from [com.github.penfeizhou.android.animation:gif:3.0.3] /Users/<USER>/.gradle/caches/8.13/transforms/ea85dbefea5c1958d4cdd80a099d89cf/transformed/gif-3.0.3/AndroidManifest.xml:2:1-7:12
MERGED from [com.github.penfeizhou.android.animation:avif:3.0.3] /Users/<USER>/.gradle/caches/8.13/transforms/fa93cb06fb117b034e9e00e2254e875d/transformed/avif-3.0.3/AndroidManifest.xml:2:1-7:12
MERGED from [com.github.penfeizhou.android.animation:frameanimation:3.0.3] /Users/<USER>/.gradle/caches/8.13/transforms/76f79e0c31791936fb2bd92707f6357d/transformed/frameanimation-3.0.3/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.appcompat:appcompat:1.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/1cc5e9e2cf9bf5411b10463ffe5bdf1d/transformed/appcompat-1.7.0/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.fragment:fragment-ktx:1.6.1] /Users/<USER>/.gradle/caches/8.13/transforms/45a5fcb4b05df7123a46aa86205607f0/transformed/fragment-ktx-1.6.1/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-v4:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/efa3c0a2e02549c1d639d2cb5cf43903/transformed/legacy-support-v4-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [com.github.bumptech.glide:avif-integration:4.16.0] /Users/<USER>/.gradle/caches/8.13/transforms/65acae60dc05898669088afb756e6a62/transformed/avif-integration-4.16.0/AndroidManifest.xml:2:1-9:12
MERGED from [jp.wasabeef:glide-transformations:4.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/2d9172c3d7ced60217e2cab8dc4ac29a/transformed/glide-transformations-4.3.0/AndroidManifest.xml:2:1-9:12
MERGED from [com.github.bumptech.glide:okhttp3-integration:4.11.0] /Users/<USER>/.gradle/caches/8.13/transforms/20c2c99566bca8247160f1fa1ef8c1cc/transformed/okhttp3-integration-4.11.0/AndroidManifest.xml:2:1-16:12
MERGED from [com.github.bumptech.glide:glide:4.16.0] /Users/<USER>/.gradle/caches/8.13/transforms/6d4036466a3da156a07d7c15151d5f6a/transformed/glide-4.16.0/AndroidManifest.xml:2:1-9:12
MERGED from [androidx.viewpager2:viewpager2:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/0a985a726a0ed91053e59647f893676a/transformed/viewpager2-1.0.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.fragment:fragment:1.6.1] /Users/<USER>/.gradle/caches/8.13/transforms/7cf11ab6a97ce7a19d873c7fc9f6245f/transformed/fragment-1.6.1/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.activity:activity:1.8.0] /Users/<USER>/.gradle/caches/8.13/transforms/8f54346d7887f87b0ac14941a641e1e5/transformed/activity-1.8.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.activity:activity-ktx:1.8.0] /Users/<USER>/.gradle/caches/8.13/transforms/7e03b29ff010dcda13836cde37647491/transformed/activity-ktx-1.8.0/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] /Users/<USER>/.gradle/caches/8.13/transforms/86cbd6534acf09b6edb34f1ee4cf1ea6/transformed/savedstate-ktx-1.2.1/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/7cbd0f7d2422a506820c11fbac42769d/transformed/legacy-support-core-ui-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/675643d2417f53f62de6e71cb70c4a03/transformed/coordinatorlayout-1.2.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.1.0] /Users/<USER>/.gradle/caches/8.13/transforms/a803a10f06d03b85c791e9e7827df3a7/transformed/swiperefreshlayout-1.1.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.webkit:webkit:1.4.0] /Users/<USER>/.gradle/caches/8.13/transforms/c7cd1ae60e32cf15d1d605bbf04384dc/transformed/webkit-1.4.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.autofill:autofill:1.1.0] /Users/<USER>/.gradle/caches/8.13/transforms/6126635189ee6e9915c55ed159ffcefd/transformed/autofill-1.1.0/AndroidManifest.xml:17:1-24:12
MERGED from [com.facebook.fresco:animated-gif:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/2edb4108b6d3af54f55ac8f7a3fd9ae0/transformed/animated-gif-3.6.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:webpsupport:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/0297df8fcc2974bf1a6749148dbc26c0/transformed/webpsupport-3.6.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:fresco:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/9ace98e86e8daf77252a03f707fe16e5/transformed/fresco-3.6.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:imagepipeline-okhttp3:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/7c035c9f5baa4c873c913fa078c879e7/transformed/imagepipeline-okhttp3-3.6.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:animated-base:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/6c0c3d9644f9203b6dc5a2f3ebd3236b/transformed/animated-base-3.6.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:animated-drawable:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/1f4c51f0bcade3115e42ccfe51bd59a1/transformed/animated-drawable-3.6.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:vito-options:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/ba9ef01bf112dd567fa66445cb5c61e2/transformed/vito-options-3.6.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:drawee:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/2f42596c4ca5d2009892461f9c7813b2/transformed/drawee-3.6.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:nativeimagefilters:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/4ba88b7da306cdab7c7db5ea63a68613/transformed/nativeimagefilters-3.6.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:memory-type-native:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/1d59e45c858309aed0d8a0127b93222a/transformed/memory-type-native-3.6.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:memory-type-java:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/35c756a193f6db29047132ee07537a49/transformed/memory-type-java-3.6.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:imagepipeline-native:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/3fdf4f45aa1f8b9633b95505ee88a374/transformed/imagepipeline-native-3.6.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:memory-type-ashmem:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/a4e405a364064355f0d76d95d666e4c3/transformed/memory-type-ashmem-3.6.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:imagepipeline:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/c8ab8ff0816e2da218676ecd718a2fdd/transformed/imagepipeline-3.6.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:nativeimagetranscoder:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/c582b1007c5176013781b139f8986a2c/transformed/nativeimagetranscoder-3.6.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:imagepipeline-base:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/79f3259516df22757d258a93b0a50829/transformed/imagepipeline-base-3.6.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:urimod:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/cde481b0d533be33fa0f770b40e08f80/transformed/urimod-3.6.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:vito-source:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/da0dd16585cfbcbe69014c3bea92fdb6/transformed/vito-source-3.6.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:middleware:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/afcf702965a22ec9b05ff95daaacf5ab/transformed/middleware-3.6.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:ui-common:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/2072985832aa083bee5cf76d9f79c99b/transformed/ui-common-3.6.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:soloader:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/d2b07f954987ada6e44a53492ccc0edd/transformed/soloader-3.6.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:fbcore:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/805d64c1d3740b135c3c6d25d007a3e6/transformed/fbcore-3.6.0/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.core:core-ktx:1.13.1] /Users/<USER>/.gradle/caches/8.13/transforms/44982b7d7b05239c12187164d2522afe/transformed/core-ktx-1.13.1/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.browser:browser:1.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/44d5193287b18dec49d9cd0bdd73b6e9/transformed/browser-1.6.0/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.transition:transition:1.5.0] /Users/<USER>/.gradle/caches/8.13/transforms/bdbe8332f585138dd54b686bb64a82aa/transformed/transition-1.5.0/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] /Users/<USER>/.gradle/caches/8.13/transforms/eabbbcce580f93f46e8da890fc2dc6b7/transformed/drawerlayout-1.1.1/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/ce19d4bbc6c73debd3675c7709924648/transformed/dynamicanimation-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] /Users/<USER>/.gradle/caches/8.13/transforms/814e25e87c08337205c573e8fbde7912/transformed/vectordrawable-animated-1.1.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] /Users/<USER>/.gradle/caches/8.13/transforms/d506d84aa1481d9d5e3867a8b5a87066/transformed/vectordrawable-1.1.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/8109800e0431b448d21295476395ef1d/transformed/emoji2-views-helper-1.3.0/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/bdcbec29e8d0b717843f3dbae0b94f84/transformed/emoji2-1.3.0/AndroidManifest.xml:17:1-35:12
MERGED from [androidx.media:media:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/17e95e9d548b080f2342fe4e9d66b27c/transformed/media-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/919c29d3ded19ccee428cb72162a56a5/transformed/legacy-support-core-utils-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/7ed80d3bf5b670828b2b3bc6f4b4dfeb/transformed/loader-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.viewpager:viewpager:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/19e333d2a1598d1596ab4253be890ecc/transformed/viewpager-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.recyclerview:recyclerview:1.1.0] /Users/<USER>/.gradle/caches/8.13/transforms/47a56a4f1be0e9a7d815855bffa6881d/transformed/recyclerview-1.1.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/bf22f6268219602ea588d58cca050520/transformed/slidingpanelayout-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.1.0] /Users/<USER>/.gradle/caches/8.13/transforms/2876b5b071ba525c32ef673b01793303/transformed/customview-1.1.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/9fe9a9a5dc989223350b29a84763d980/transformed/asynclayoutinflater-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.13/transforms/25ad4bdeddd79d498a0e9dfe4af5a7d2/transformed/core-1.13.1/AndroidManifest.xml:17:1-30:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/6a7fec56ee66dd7f7a0a2b21f81fa72c/transformed/lifecycle-viewmodel-ktx-2.6.2/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/bae0062137d08dab4b3c13dc4624a335/transformed/lifecycle-viewmodel-2.6.2/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/fece13e9c08c13338369de69cd14e88c/transformed/lifecycle-runtime-ktx-2.6.2/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/ec3f62d4745054bc29342c5607bcde4d/transformed/lifecycle-process-2.6.2/AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/10b213ecea0e4e52da532b6ba0f83370/transformed/lifecycle-livedata-core-ktx-2.6.2/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/74c5ef7ab2dc463f805db9e9370b6b3a/transformed/lifecycle-livedata-core-2.6.2/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/c0f61044d9766c534145df491b529aab/transformed/lifecycle-livedata-2.6.2/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/fb72dc500fcacad8fdd089fb6045d7e1/transformed/lifecycle-runtime-2.6.2/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/b6d7366c2eeb6fe782cd6baab9130943/transformed/lifecycle-viewmodel-savedstate-2.6.2/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] /Users/<USER>/.gradle/caches/8.13/transforms/0b7ca141aa9d3a05c286852a98884a1f/transformed/savedstate-1.2.1/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/3070f604e3c80036d56e6e100694a483/transformed/profileinstaller-1.3.1/AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.13/transforms/b02b404f32dd5de34e82e545d50d8192/transformed/startup-runtime-1.1.1/AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/eb45ff10c0db144c5a5537429e93efe3/transformed/tracing-1.2.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.tracing:tracing-ktx:1.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/1bbf8401d34e5d62926e60ab182c1f96/transformed/tracing-ktx-1.2.0/AndroidManifest.xml:17:1-22:12
MERGED from [com.facebook.fresco:ui-core:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/be04f8773d381722a4b4262f7c46e102/transformed/ui-core-3.6.0/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.annotation:annotation-experimental:1.4.0] /Users/<USER>/.gradle/caches/8.13/transforms/9a3668def9ff4e9f17f553dcb425dac8/transformed/annotation-experimental-1.4.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:vito-renderer:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/52f3a2fa8c0df5a8414fb52db1dd2780/transformed/vito-renderer-3.6.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.react:hermes-android:0.79.4] /Users/<USER>/.gradle/caches/8.13/transforms/08e098abbe4d9b976b042791d1d5adf2/transformed/hermes-android-0.79.4-debug/AndroidManifest.xml:2:1-7:12
MERGED from [com.github.Dimezis:BlurView:version-2.0.6] /Users/<USER>/.gradle/caches/8.13/transforms/67a76227458514088161a2b0747e4617/transformed/BlurView-version-2.0.6/AndroidManifest.xml:2:1-11:12
MERGED from [androidx.interpolator:interpolator:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/b0dc910de971c283f6356d856b6fba5e/transformed/interpolator-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cardview:cardview:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/57b0ed4888c5d35f21d94e7e07d01862/transformed/cardview-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/5c242fbce19fdaa380f4f5131b50e6e6/transformed/cursoradapter-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [com.github.bumptech.glide:gifdecoder:4.16.0] /Users/<USER>/.gradle/caches/8.13/transforms/e78d9459b2e333fa78bced9c54bd129f/transformed/gifdecoder-4.16.0/AndroidManifest.xml:2:1-9:12
MERGED from [androidx.exifinterface:exifinterface:1.3.6] /Users/<USER>/.gradle/caches/8.13/transforms/436799b26b86e567b064386e20bb09aa/transformed/exifinterface-1.3.6/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/8.13/transforms/be6898bf4d583734f3f30b86f3aa7db4/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:17:1-27:12
MERGED from [androidx.documentfile:documentfile:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/7c602fb14d62922ac8d7f7c4ab0d51ca/transformed/documentfile-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/efff7a0c1ff905ae9950c94d7858e693/transformed/localbroadcastmanager-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/adf446957242012738eebe9b97a609ca/transformed/print-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/fcdbde368ff3f7b130f131373693d72e/transformed/core-runtime-2.2.0/AndroidManifest.xml:17:1-22:12
MERGED from [com.facebook.fbjni:fbjni:0.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/f7366e53586a12172ab374397325a4bf/transformed/fbjni-0.7.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.soloader:soloader:0.12.1] /Users/<USER>/.gradle/caches/8.13/transforms/fdb63001aee1d718e95ecb2b3adfa162/transformed/soloader-0.12.1/AndroidManifest.xml:2:1-17:12
MERGED from [com.caverock:androidsvg-aar:1.4] /Users/<USER>/.gradle/caches/8.13/transforms/0ced2c618cc0851d3ee11456ab9ae13d/transformed/androidsvg-aar-1.4/AndroidManifest.xml:2:1-11:12
MERGED from [org.aomedia.avif.android:avif:1.0.1.262e11d] /Users/<USER>/.gradle/caches/8.13/transforms/fd97a93c331c36d4de146222ebe73ebb/transformed/avif-1.0.1.262e11d/AndroidManifest.xml:2:1-7:12
	package
		INJECTED from /Users/<USER>/not_work/my-app/android/app/src/debug/AndroidManifest.xml
	android:versionName
		INJECTED from /Users/<USER>/not_work/my-app/android/app/src/debug/AndroidManifest.xml
	xmlns:tools
		ADDED from /Users/<USER>/not_work/my-app/android/app/src/debug/AndroidManifest.xml:2:5-51
	android:versionCode
		INJECTED from /Users/<USER>/not_work/my-app/android/app/src/debug/AndroidManifest.xml
	xmlns:android
		ADDED from /Users/<USER>/not_work/my-app/android/app/src/main/AndroidManifest.xml:1:11-69
uses-permission#android.permission.INTERNET
ADDED from /Users/<USER>/not_work/my-app/android/app/src/main/AndroidManifest.xml:2:3-64
MERGED from [BareExpo:expo.modules.image:2.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/3436d2bb0e66cba9430428ecbc7fb171/transformed/expo.modules.image-2.3.0/AndroidManifest.xml:7:5-67
MERGED from [BareExpo:expo.modules.image:2.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/3436d2bb0e66cba9430428ecbc7fb171/transformed/expo.modules.image-2.3.0/AndroidManifest.xml:7:5-67
MERGED from [:expo-file-system] /Users/<USER>/not_work/my-app/node_modules/expo-file-system/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:8:5-67
MERGED from [:expo-file-system] /Users/<USER>/not_work/my-app/node_modules/expo-file-system/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:8:5-67
	android:name
		ADDED from /Users/<USER>/not_work/my-app/android/app/src/main/AndroidManifest.xml:2:20-62
uses-permission#android.permission.READ_EXTERNAL_STORAGE
ADDED from /Users/<USER>/not_work/my-app/android/app/src/main/AndroidManifest.xml:3:3-77
MERGED from [BareExpo:expo.modules.image:2.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/3436d2bb0e66cba9430428ecbc7fb171/transformed/expo.modules.image-2.3.0/AndroidManifest.xml:15:5-17:38
MERGED from [BareExpo:expo.modules.image:2.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/3436d2bb0e66cba9430428ecbc7fb171/transformed/expo.modules.image-2.3.0/AndroidManifest.xml:15:5-17:38
MERGED from [:expo-file-system] /Users/<USER>/not_work/my-app/node_modules/expo-file-system/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:10:5-80
MERGED from [:expo-file-system] /Users/<USER>/not_work/my-app/node_modules/expo-file-system/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:10:5-80
	android:maxSdkVersion
		ADDED from [BareExpo:expo.modules.image:2.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/3436d2bb0e66cba9430428ecbc7fb171/transformed/expo.modules.image-2.3.0/AndroidManifest.xml:17:9-35
	android:name
		ADDED from /Users/<USER>/not_work/my-app/android/app/src/main/AndroidManifest.xml:3:20-75
uses-permission#android.permission.SYSTEM_ALERT_WINDOW
ADDED from /Users/<USER>/not_work/my-app/android/app/src/main/AndroidManifest.xml:4:3-75
MERGED from /Users/<USER>/not_work/my-app/android/app/src/main/AndroidManifest.xml:4:3-75
MERGED from /Users/<USER>/not_work/my-app/android/app/src/main/AndroidManifest.xml:4:3-75
MERGED from [com.facebook.react:react-android:0.79.4] /Users/<USER>/.gradle/caches/8.13/transforms/ee3752888ecf2d82fe4920d23f93a394/transformed/react-android-0.79.4-debug/AndroidManifest.xml:16:5-78
MERGED from [com.facebook.react:react-android:0.79.4] /Users/<USER>/.gradle/caches/8.13/transforms/ee3752888ecf2d82fe4920d23f93a394/transformed/react-android-0.79.4-debug/AndroidManifest.xml:16:5-78
	android:name
		ADDED from /Users/<USER>/not_work/my-app/android/app/src/main/AndroidManifest.xml:4:20-73
uses-permission#android.permission.VIBRATE
ADDED from /Users/<USER>/not_work/my-app/android/app/src/main/AndroidManifest.xml:5:3-63
MERGED from [host.exp.exponent:expo.modules.haptics:14.1.4] /Users/<USER>/.gradle/caches/8.13/transforms/45fbacacfd03779f88e5bdab927202e7/transformed/expo.modules.haptics-14.1.4/AndroidManifest.xml:7:5-66
MERGED from [host.exp.exponent:expo.modules.haptics:14.1.4] /Users/<USER>/.gradle/caches/8.13/transforms/45fbacacfd03779f88e5bdab927202e7/transformed/expo.modules.haptics-14.1.4/AndroidManifest.xml:7:5-66
	android:name
		ADDED from /Users/<USER>/not_work/my-app/android/app/src/main/AndroidManifest.xml:5:20-61
uses-permission#android.permission.WRITE_EXTERNAL_STORAGE
ADDED from /Users/<USER>/not_work/my-app/android/app/src/main/AndroidManifest.xml:6:3-78
MERGED from [:expo-file-system] /Users/<USER>/not_work/my-app/node_modules/expo-file-system/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:9:5-81
MERGED from [:expo-file-system] /Users/<USER>/not_work/my-app/node_modules/expo-file-system/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:9:5-81
	android:name
		ADDED from /Users/<USER>/not_work/my-app/android/app/src/main/AndroidManifest.xml:6:20-76
queries
ADDED from /Users/<USER>/not_work/my-app/android/app/src/main/AndroidManifest.xml:7:3-13:13
MERGED from [:expo-file-system] /Users/<USER>/not_work/my-app/node_modules/expo-file-system/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:12:5-18:15
MERGED from [:expo-file-system] /Users/<USER>/not_work/my-app/node_modules/expo-file-system/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:12:5-18:15
MERGED from [host.exp.exponent:expo.modules.webbrowser:14.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/b2e62f529b3dc5334aec2c40acace88a/transformed/expo.modules.webbrowser-14.2.0/AndroidManifest.xml:7:5-13:15
MERGED from [host.exp.exponent:expo.modules.webbrowser:14.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/b2e62f529b3dc5334aec2c40acace88a/transformed/expo.modules.webbrowser-14.2.0/AndroidManifest.xml:7:5-13:15
intent#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+data:scheme:https
ADDED from /Users/<USER>/not_work/my-app/android/app/src/main/AndroidManifest.xml:8:5-12:14
action#android.intent.action.VIEW
ADDED from /Users/<USER>/not_work/my-app/android/app/src/main/AndroidManifest.xml:9:7-58
	android:name
		ADDED from /Users/<USER>/not_work/my-app/android/app/src/main/AndroidManifest.xml:9:15-56
category#android.intent.category.BROWSABLE
ADDED from /Users/<USER>/not_work/my-app/android/app/src/main/AndroidManifest.xml:10:7-67
	android:name
		ADDED from /Users/<USER>/not_work/my-app/android/app/src/main/AndroidManifest.xml:10:17-65
data
ADDED from /Users/<USER>/not_work/my-app/android/app/src/main/AndroidManifest.xml:11:7-37
	android:scheme
		ADDED from /Users/<USER>/not_work/my-app/android/app/src/main/AndroidManifest.xml:11:13-35
application
ADDED from /Users/<USER>/not_work/my-app/android/app/src/main/AndroidManifest.xml:14:3-30:17
MERGED from /Users/<USER>/not_work/my-app/android/app/src/main/AndroidManifest.xml:14:3-30:17
MERGED from /Users/<USER>/not_work/my-app/android/app/src/main/AndroidManifest.xml:14:3-30:17
INJECTED from /Users/<USER>/not_work/my-app/android/app/src/debug/AndroidManifest.xml:6:5-162
MERGED from [:react-native-webview] /Users/<USER>/not_work/my-app/node_modules/react-native-webview/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:7:5-17:19
MERGED from [:react-native-webview] /Users/<USER>/not_work/my-app/node_modules/react-native-webview/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:7:5-17:19
MERGED from [:expo-modules-core] /Users/<USER>/not_work/my-app/node_modules/expo-modules-core/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:8:5-16:19
MERGED from [:expo-modules-core] /Users/<USER>/not_work/my-app/node_modules/expo-modules-core/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:8:5-16:19
MERGED from [com.facebook.react:react-android:0.79.4] /Users/<USER>/.gradle/caches/8.13/transforms/ee3752888ecf2d82fe4920d23f93a394/transformed/react-android-0.79.4-debug/AndroidManifest.xml:18:5-22:19
MERGED from [com.facebook.react:react-android:0.79.4] /Users/<USER>/.gradle/caches/8.13/transforms/ee3752888ecf2d82fe4920d23f93a394/transformed/react-android-0.79.4-debug/AndroidManifest.xml:18:5-22:19
MERGED from [:expo-file-system] /Users/<USER>/not_work/my-app/node_modules/expo-file-system/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:20:5-31:19
MERGED from [:expo-file-system] /Users/<USER>/not_work/my-app/node_modules/expo-file-system/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:20:5-31:19
MERGED from [com.google.android.material:material:1.12.0] /Users/<USER>/.gradle/caches/8.13/transforms/1688e4f4998421f6cdbc59f17cef2f61/transformed/material-1.12.0/AndroidManifest.xml:22:5-20
MERGED from [com.google.android.material:material:1.12.0] /Users/<USER>/.gradle/caches/8.13/transforms/1688e4f4998421f6cdbc59f17cef2f61/transformed/material-1.12.0/AndroidManifest.xml:22:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] /Users/<USER>/.gradle/caches/8.13/transforms/4a6ff7a886435c160fc114598df514e8/transformed/constraintlayout-2.0.1/AndroidManifest.xml:9:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] /Users/<USER>/.gradle/caches/8.13/transforms/4a6ff7a886435c160fc114598df514e8/transformed/constraintlayout-2.0.1/AndroidManifest.xml:9:5-20
MERGED from [com.github.bumptech.glide:okhttp3-integration:4.11.0] /Users/<USER>/.gradle/caches/8.13/transforms/20c2c99566bca8247160f1fa1ef8c1cc/transformed/okhttp3-integration-4.11.0/AndroidManifest.xml:10:5-14:19
MERGED from [com.github.bumptech.glide:okhttp3-integration:4.11.0] /Users/<USER>/.gradle/caches/8.13/transforms/20c2c99566bca8247160f1fa1ef8c1cc/transformed/okhttp3-integration-4.11.0/AndroidManifest.xml:10:5-14:19
MERGED from [androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/bdcbec29e8d0b717843f3dbae0b94f84/transformed/emoji2-1.3.0/AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/bdcbec29e8d0b717843f3dbae0b94f84/transformed/emoji2-1.3.0/AndroidManifest.xml:23:5-33:19
MERGED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.13/transforms/25ad4bdeddd79d498a0e9dfe4af5a7d2/transformed/core-1.13.1/AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.13/transforms/25ad4bdeddd79d498a0e9dfe4af5a7d2/transformed/core-1.13.1/AndroidManifest.xml:28:5-89
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/ec3f62d4745054bc29342c5607bcde4d/transformed/lifecycle-process-2.6.2/AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/ec3f62d4745054bc29342c5607bcde4d/transformed/lifecycle-process-2.6.2/AndroidManifest.xml:23:5-33:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/3070f604e3c80036d56e6e100694a483/transformed/profileinstaller-1.3.1/AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/3070f604e3c80036d56e6e100694a483/transformed/profileinstaller-1.3.1/AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.13/transforms/b02b404f32dd5de34e82e545d50d8192/transformed/startup-runtime-1.1.1/AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.13/transforms/b02b404f32dd5de34e82e545d50d8192/transformed/startup-runtime-1.1.1/AndroidManifest.xml:25:5-31:19
MERGED from [com.github.Dimezis:BlurView:version-2.0.6] /Users/<USER>/.gradle/caches/8.13/transforms/67a76227458514088161a2b0747e4617/transformed/BlurView-version-2.0.6/AndroidManifest.xml:9:5-20
MERGED from [com.github.Dimezis:BlurView:version-2.0.6] /Users/<USER>/.gradle/caches/8.13/transforms/67a76227458514088161a2b0747e4617/transformed/BlurView-version-2.0.6/AndroidManifest.xml:9:5-20
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/8.13/transforms/be6898bf4d583734f3f30b86f3aa7db4/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/8.13/transforms/be6898bf4d583734f3f30b86f3aa7db4/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:24:5-25:19
MERGED from [com.facebook.soloader:soloader:0.12.1] /Users/<USER>/.gradle/caches/8.13/transforms/fdb63001aee1d718e95ecb2b3adfa162/transformed/soloader-0.12.1/AndroidManifest.xml:11:5-15:19
MERGED from [com.facebook.soloader:soloader:0.12.1] /Users/<USER>/.gradle/caches/8.13/transforms/fdb63001aee1d718e95ecb2b3adfa162/transformed/soloader-0.12.1/AndroidManifest.xml:11:5-15:19
MERGED from [org.aomedia.avif.android:avif:1.0.1.262e11d] /Users/<USER>/.gradle/caches/8.13/transforms/fd97a93c331c36d4de146222ebe73ebb/transformed/avif-1.0.1.262e11d/AndroidManifest.xml:5:5-6:19
MERGED from [org.aomedia.avif.android:avif:1.0.1.262e11d] /Users/<USER>/.gradle/caches/8.13/transforms/fd97a93c331c36d4de146222ebe73ebb/transformed/avif-1.0.1.262e11d/AndroidManifest.xml:5:5-6:19
	android:extractNativeLibs
		INJECTED from /Users/<USER>/not_work/my-app/android/app/src/debug/AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.13/transforms/25ad4bdeddd79d498a0e9dfe4af5a7d2/transformed/core-1.13.1/AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from /Users/<USER>/not_work/my-app/android/app/src/main/AndroidManifest.xml:14:221-247
		ADDED from /Users/<USER>/not_work/my-app/android/app/src/main/AndroidManifest.xml:14:221-247
	android:label
		ADDED from /Users/<USER>/not_work/my-app/android/app/src/main/AndroidManifest.xml:14:48-80
		ADDED from /Users/<USER>/not_work/my-app/android/app/src/main/AndroidManifest.xml:14:48-80
	tools:ignore
		ADDED from /Users/<USER>/not_work/my-app/android/app/src/debug/AndroidManifest.xml:6:75-114
	android:roundIcon
		ADDED from /Users/<USER>/not_work/my-app/android/app/src/main/AndroidManifest.xml:14:116-161
		ADDED from /Users/<USER>/not_work/my-app/android/app/src/main/AndroidManifest.xml:14:116-161
	tools:targetApi
		ADDED from /Users/<USER>/not_work/my-app/android/app/src/debug/AndroidManifest.xml:6:54-74
	android:icon
		ADDED from /Users/<USER>/not_work/my-app/android/app/src/main/AndroidManifest.xml:14:81-115
		ADDED from /Users/<USER>/not_work/my-app/android/app/src/main/AndroidManifest.xml:14:81-115
	android:allowBackup
		ADDED from /Users/<USER>/not_work/my-app/android/app/src/main/AndroidManifest.xml:14:162-188
		ADDED from /Users/<USER>/not_work/my-app/android/app/src/main/AndroidManifest.xml:14:162-188
	android:theme
		ADDED from /Users/<USER>/not_work/my-app/android/app/src/main/AndroidManifest.xml:14:189-220
		ADDED from /Users/<USER>/not_work/my-app/android/app/src/main/AndroidManifest.xml:14:189-220
	tools:replace
		ADDED from /Users/<USER>/not_work/my-app/android/app/src/debug/AndroidManifest.xml:6:115-159
	android:usesCleartextTraffic
		ADDED from /Users/<USER>/not_work/my-app/android/app/src/debug/AndroidManifest.xml:6:18-53
	android:name
		ADDED from /Users/<USER>/not_work/my-app/android/app/src/main/AndroidManifest.xml:14:16-47
		ADDED from /Users/<USER>/not_work/my-app/android/app/src/main/AndroidManifest.xml:14:16-47
meta-data#expo.modules.updates.ENABLED
ADDED from /Users/<USER>/not_work/my-app/android/app/src/main/AndroidManifest.xml:15:5-83
	android:value
		ADDED from /Users/<USER>/not_work/my-app/android/app/src/main/AndroidManifest.xml:15:60-81
	android:name
		ADDED from /Users/<USER>/not_work/my-app/android/app/src/main/AndroidManifest.xml:15:16-59
meta-data#expo.modules.updates.EXPO_UPDATES_CHECK_ON_LAUNCH
ADDED from /Users/<USER>/not_work/my-app/android/app/src/main/AndroidManifest.xml:16:5-105
	android:value
		ADDED from /Users/<USER>/not_work/my-app/android/app/src/main/AndroidManifest.xml:16:81-103
	android:name
		ADDED from /Users/<USER>/not_work/my-app/android/app/src/main/AndroidManifest.xml:16:16-80
meta-data#expo.modules.updates.EXPO_UPDATES_LAUNCH_WAIT_MS
ADDED from /Users/<USER>/not_work/my-app/android/app/src/main/AndroidManifest.xml:17:5-99
	android:value
		ADDED from /Users/<USER>/not_work/my-app/android/app/src/main/AndroidManifest.xml:17:80-97
	android:name
		ADDED from /Users/<USER>/not_work/my-app/android/app/src/main/AndroidManifest.xml:17:16-79
activity#com.anonymous.myapp.MainActivity
ADDED from /Users/<USER>/not_work/my-app/android/app/src/main/AndroidManifest.xml:18:5-29:16
	android:screenOrientation
		ADDED from /Users/<USER>/not_work/my-app/android/app/src/main/AndroidManifest.xml:18:280-316
	android:launchMode
		ADDED from /Users/<USER>/not_work/my-app/android/app/src/main/AndroidManifest.xml:18:135-166
	android:windowSoftInputMode
		ADDED from /Users/<USER>/not_work/my-app/android/app/src/main/AndroidManifest.xml:18:167-209
	android:exported
		ADDED from /Users/<USER>/not_work/my-app/android/app/src/main/AndroidManifest.xml:18:256-279
	android:configChanges
		ADDED from /Users/<USER>/not_work/my-app/android/app/src/main/AndroidManifest.xml:18:44-134
	android:theme
		ADDED from /Users/<USER>/not_work/my-app/android/app/src/main/AndroidManifest.xml:18:210-255
	android:name
		ADDED from /Users/<USER>/not_work/my-app/android/app/src/main/AndroidManifest.xml:18:15-43
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from /Users/<USER>/not_work/my-app/android/app/src/main/AndroidManifest.xml:19:7-22:23
action#android.intent.action.MAIN
ADDED from /Users/<USER>/not_work/my-app/android/app/src/main/AndroidManifest.xml:20:9-60
	android:name
		ADDED from /Users/<USER>/not_work/my-app/android/app/src/main/AndroidManifest.xml:20:17-58
category#android.intent.category.LAUNCHER
ADDED from /Users/<USER>/not_work/my-app/android/app/src/main/AndroidManifest.xml:21:9-68
	android:name
		ADDED from /Users/<USER>/not_work/my-app/android/app/src/main/AndroidManifest.xml:21:19-66
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:scheme:myapp
ADDED from /Users/<USER>/not_work/my-app/android/app/src/main/AndroidManifest.xml:23:7-28:23
category#android.intent.category.DEFAULT
ADDED from /Users/<USER>/not_work/my-app/android/app/src/main/AndroidManifest.xml:25:9-67
	android:name
		ADDED from /Users/<USER>/not_work/my-app/android/app/src/main/AndroidManifest.xml:25:19-65
uses-sdk
INJECTED from /Users/<USER>/not_work/my-app/android/app/src/debug/AndroidManifest.xml reason: use-sdk injection requested
INJECTED from /Users/<USER>/not_work/my-app/android/app/src/debug/AndroidManifest.xml
INJECTED from /Users/<USER>/not_work/my-app/android/app/src/debug/AndroidManifest.xml
MERGED from [:react-native-gesture-handler] /Users/<USER>/not_work/my-app/node_modules/react-native-gesture-handler/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:react-native-gesture-handler] /Users/<USER>/not_work/my-app/node_modules/react-native-gesture-handler/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:react-native-safe-area-context] /Users/<USER>/not_work/my-app/node_modules/react-native-safe-area-context/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:react-native-safe-area-context] /Users/<USER>/not_work/my-app/node_modules/react-native-safe-area-context/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:react-native-screens] /Users/<USER>/not_work/my-app/node_modules/react-native-screens/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:react-native-screens] /Users/<USER>/not_work/my-app/node_modules/react-native-screens/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:react-native-webview] /Users/<USER>/not_work/my-app/node_modules/react-native-webview/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:react-native-webview] /Users/<USER>/not_work/my-app/node_modules/react-native-webview/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:react-native-edge-to-edge] /Users/<USER>/not_work/my-app/node_modules/react-native-edge-to-edge/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:react-native-edge-to-edge] /Users/<USER>/not_work/my-app/node_modules/react-native-edge-to-edge/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:expo] /Users/<USER>/not_work/my-app/node_modules/expo/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:expo] /Users/<USER>/not_work/my-app/node_modules/expo/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:react-native-reanimated] /Users/<USER>/not_work/my-app/node_modules/react-native-reanimated/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:react-native-reanimated] /Users/<USER>/not_work/my-app/node_modules/react-native-reanimated/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:expo-modules-core] /Users/<USER>/not_work/my-app/node_modules/expo-modules-core/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:6:5-44
MERGED from [:expo-modules-core] /Users/<USER>/not_work/my-app/node_modules/expo-modules-core/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:6:5-44
MERGED from [host.exp.exponent:expo.modules.font:13.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/d97e291462cdacba71e8ea9143529a40/transformed/expo.modules.font-13.3.1/AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.font:13.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/d97e291462cdacba71e8ea9143529a40/transformed/expo.modules.font-13.3.1/AndroidManifest.xml:5:5-44
MERGED from [BareExpo:expo.modules.image:2.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/3436d2bb0e66cba9430428ecbc7fb171/transformed/expo.modules.image-2.3.0/AndroidManifest.xml:4:5-44
MERGED from [BareExpo:expo.modules.image:2.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/3436d2bb0e66cba9430428ecbc7fb171/transformed/expo.modules.image-2.3.0/AndroidManifest.xml:4:5-44
MERGED from [host.exp.exponent:expo.modules.splashscreen:0.30.9] /Users/<USER>/.gradle/caches/8.13/transforms/b3390bd7dd069ce47ea2215a9701dc04/transformed/expo.modules.splashscreen-0.30.9/AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.splashscreen:0.30.9] /Users/<USER>/.gradle/caches/8.13/transforms/b3390bd7dd069ce47ea2215a9701dc04/transformed/expo.modules.splashscreen-0.30.9/AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.systemui:5.0.9] /Users/<USER>/.gradle/caches/8.13/transforms/242ac0c339cf89d88a6b948ccf19dfd1/transformed/expo.modules.systemui-5.0.9/AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.systemui:5.0.9] /Users/<USER>/.gradle/caches/8.13/transforms/242ac0c339cf89d88a6b948ccf19dfd1/transformed/expo.modules.systemui-5.0.9/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.react:react-android:0.79.4] /Users/<USER>/.gradle/caches/8.13/transforms/ee3752888ecf2d82fe4920d23f93a394/transformed/react-android-0.79.4-debug/AndroidManifest.xml:10:5-44
MERGED from [com.facebook.react:react-android:0.79.4] /Users/<USER>/.gradle/caches/8.13/transforms/ee3752888ecf2d82fe4920d23f93a394/transformed/react-android-0.79.4-debug/AndroidManifest.xml:10:5-44
MERGED from [:expo-constants] /Users/<USER>/not_work/my-app/node_modules/expo-constants/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:expo-constants] /Users/<USER>/not_work/my-app/node_modules/expo-constants/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:expo-file-system] /Users/<USER>/not_work/my-app/node_modules/expo-file-system/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:6:5-44
MERGED from [:expo-file-system] /Users/<USER>/not_work/my-app/node_modules/expo-file-system/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:6:5-44
MERGED from [:expo-linking] /Users/<USER>/not_work/my-app/node_modules/expo-linking/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:expo-linking] /Users/<USER>/not_work/my-app/node_modules/expo-linking/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [expo.modules.asset:expo.modules.asset:11.1.5] /Users/<USER>/.gradle/caches/8.13/transforms/e8a3e9b5e96af02342a176ccdc544ece/transformed/expo.modules.asset-11.1.5/AndroidManifest.xml:5:5-44
MERGED from [expo.modules.asset:expo.modules.asset:11.1.5] /Users/<USER>/.gradle/caches/8.13/transforms/e8a3e9b5e96af02342a176ccdc544ece/transformed/expo.modules.asset-11.1.5/AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.blur:14.1.5] /Users/<USER>/.gradle/caches/8.13/transforms/6de1916b90041fd9e517ff96a11caa6d/transformed/expo.modules.blur-14.1.5/AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.blur:14.1.5] /Users/<USER>/.gradle/caches/8.13/transforms/6de1916b90041fd9e517ff96a11caa6d/transformed/expo.modules.blur-14.1.5/AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.haptics:14.1.4] /Users/<USER>/.gradle/caches/8.13/transforms/45fbacacfd03779f88e5bdab927202e7/transformed/expo.modules.haptics-14.1.4/AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.haptics:14.1.4] /Users/<USER>/.gradle/caches/8.13/transforms/45fbacacfd03779f88e5bdab927202e7/transformed/expo.modules.haptics-14.1.4/AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.keepawake:14.1.4] /Users/<USER>/.gradle/caches/8.13/transforms/9e4cfa109c9b9afbaf07c5dc128efb23/transformed/expo.modules.keepawake-14.1.4/AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.keepawake:14.1.4] /Users/<USER>/.gradle/caches/8.13/transforms/9e4cfa109c9b9afbaf07c5dc128efb23/transformed/expo.modules.keepawake-14.1.4/AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.webbrowser:14.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/b2e62f529b3dc5334aec2c40acace88a/transformed/expo.modules.webbrowser-14.2.0/AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.webbrowser:14.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/b2e62f529b3dc5334aec2c40acace88a/transformed/expo.modules.webbrowser-14.2.0/AndroidManifest.xml:5:5-44
MERGED from [com.google.android.material:material:1.12.0] /Users/<USER>/.gradle/caches/8.13/transforms/1688e4f4998421f6cdbc59f17cef2f61/transformed/material-1.12.0/AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.12.0] /Users/<USER>/.gradle/caches/8.13/transforms/1688e4f4998421f6cdbc59f17cef2f61/transformed/material-1.12.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core-splashscreen:1.2.0-alpha02] /Users/<USER>/.gradle/caches/8.13/transforms/e03d0108970ad49120068551e56f0044/transformed/core-splashscreen-1.2.0-alpha02/AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core-splashscreen:1.2.0-alpha02] /Users/<USER>/.gradle/caches/8.13/transforms/e03d0108970ad49120068551e56f0044/transformed/core-splashscreen-1.2.0-alpha02/AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/0ea8215bc09ef74c628f4e4a0402513e/transformed/appcompat-resources-1.7.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/0ea8215bc09ef74c628f4e4a0402513e/transformed/appcompat-resources-1.7.0/AndroidManifest.xml:5:5-44
MERGED from [com.github.penfeizhou.android.animation:glide-plugin:3.0.3] /Users/<USER>/.gradle/caches/8.13/transforms/f40077589a7ac8623ba297258e75d28b/transformed/glide-plugin-3.0.3/AndroidManifest.xml:5:5-44
MERGED from [com.github.penfeizhou.android.animation:glide-plugin:3.0.3] /Users/<USER>/.gradle/caches/8.13/transforms/f40077589a7ac8623ba297258e75d28b/transformed/glide-plugin-3.0.3/AndroidManifest.xml:5:5-44
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] /Users/<USER>/.gradle/caches/8.13/transforms/4a6ff7a886435c160fc114598df514e8/transformed/constraintlayout-2.0.1/AndroidManifest.xml:5:5-7:41
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] /Users/<USER>/.gradle/caches/8.13/transforms/4a6ff7a886435c160fc114598df514e8/transformed/constraintlayout-2.0.1/AndroidManifest.xml:5:5-7:41
MERGED from [com.github.penfeizhou.android.animation:awebp:3.0.3] /Users/<USER>/.gradle/caches/8.13/transforms/7ee6ee78b8b7385b6567f6e3572e56c2/transformed/awebp-3.0.3/AndroidManifest.xml:5:5-44
MERGED from [com.github.penfeizhou.android.animation:awebp:3.0.3] /Users/<USER>/.gradle/caches/8.13/transforms/7ee6ee78b8b7385b6567f6e3572e56c2/transformed/awebp-3.0.3/AndroidManifest.xml:5:5-44
MERGED from [com.github.penfeizhou.android.animation:apng:3.0.3] /Users/<USER>/.gradle/caches/8.13/transforms/e02d6619476aba0105310be2159d96f9/transformed/apng-3.0.3/AndroidManifest.xml:5:5-44
MERGED from [com.github.penfeizhou.android.animation:apng:3.0.3] /Users/<USER>/.gradle/caches/8.13/transforms/e02d6619476aba0105310be2159d96f9/transformed/apng-3.0.3/AndroidManifest.xml:5:5-44
MERGED from [com.github.penfeizhou.android.animation:gif:3.0.3] /Users/<USER>/.gradle/caches/8.13/transforms/ea85dbefea5c1958d4cdd80a099d89cf/transformed/gif-3.0.3/AndroidManifest.xml:5:5-44
MERGED from [com.github.penfeizhou.android.animation:gif:3.0.3] /Users/<USER>/.gradle/caches/8.13/transforms/ea85dbefea5c1958d4cdd80a099d89cf/transformed/gif-3.0.3/AndroidManifest.xml:5:5-44
MERGED from [com.github.penfeizhou.android.animation:avif:3.0.3] /Users/<USER>/.gradle/caches/8.13/transforms/fa93cb06fb117b034e9e00e2254e875d/transformed/avif-3.0.3/AndroidManifest.xml:5:5-44
MERGED from [com.github.penfeizhou.android.animation:avif:3.0.3] /Users/<USER>/.gradle/caches/8.13/transforms/fa93cb06fb117b034e9e00e2254e875d/transformed/avif-3.0.3/AndroidManifest.xml:5:5-44
MERGED from [com.github.penfeizhou.android.animation:frameanimation:3.0.3] /Users/<USER>/.gradle/caches/8.13/transforms/76f79e0c31791936fb2bd92707f6357d/transformed/frameanimation-3.0.3/AndroidManifest.xml:5:5-44
MERGED from [com.github.penfeizhou.android.animation:frameanimation:3.0.3] /Users/<USER>/.gradle/caches/8.13/transforms/76f79e0c31791936fb2bd92707f6357d/transformed/frameanimation-3.0.3/AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat:1.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/1cc5e9e2cf9bf5411b10463ffe5bdf1d/transformed/appcompat-1.7.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat:1.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/1cc5e9e2cf9bf5411b10463ffe5bdf1d/transformed/appcompat-1.7.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment-ktx:1.6.1] /Users/<USER>/.gradle/caches/8.13/transforms/45a5fcb4b05df7123a46aa86205607f0/transformed/fragment-ktx-1.6.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment-ktx:1.6.1] /Users/<USER>/.gradle/caches/8.13/transforms/45a5fcb4b05df7123a46aa86205607f0/transformed/fragment-ktx-1.6.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-v4:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/efa3c0a2e02549c1d639d2cb5cf43903/transformed/legacy-support-v4-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-v4:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/efa3c0a2e02549c1d639d2cb5cf43903/transformed/legacy-support-v4-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [com.github.bumptech.glide:avif-integration:4.16.0] /Users/<USER>/.gradle/caches/8.13/transforms/65acae60dc05898669088afb756e6a62/transformed/avif-integration-4.16.0/AndroidManifest.xml:5:5-7:41
MERGED from [com.github.bumptech.glide:avif-integration:4.16.0] /Users/<USER>/.gradle/caches/8.13/transforms/65acae60dc05898669088afb756e6a62/transformed/avif-integration-4.16.0/AndroidManifest.xml:5:5-7:41
MERGED from [jp.wasabeef:glide-transformations:4.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/2d9172c3d7ced60217e2cab8dc4ac29a/transformed/glide-transformations-4.3.0/AndroidManifest.xml:5:5-7:41
MERGED from [jp.wasabeef:glide-transformations:4.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/2d9172c3d7ced60217e2cab8dc4ac29a/transformed/glide-transformations-4.3.0/AndroidManifest.xml:5:5-7:41
MERGED from [com.github.bumptech.glide:okhttp3-integration:4.11.0] /Users/<USER>/.gradle/caches/8.13/transforms/20c2c99566bca8247160f1fa1ef8c1cc/transformed/okhttp3-integration-4.11.0/AndroidManifest.xml:6:5-8:41
MERGED from [com.github.bumptech.glide:okhttp3-integration:4.11.0] /Users/<USER>/.gradle/caches/8.13/transforms/20c2c99566bca8247160f1fa1ef8c1cc/transformed/okhttp3-integration-4.11.0/AndroidManifest.xml:6:5-8:41
MERGED from [com.github.bumptech.glide:glide:4.16.0] /Users/<USER>/.gradle/caches/8.13/transforms/6d4036466a3da156a07d7c15151d5f6a/transformed/glide-4.16.0/AndroidManifest.xml:5:5-7:41
MERGED from [com.github.bumptech.glide:glide:4.16.0] /Users/<USER>/.gradle/caches/8.13/transforms/6d4036466a3da156a07d7c15151d5f6a/transformed/glide-4.16.0/AndroidManifest.xml:5:5-7:41
MERGED from [androidx.viewpager2:viewpager2:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/0a985a726a0ed91053e59647f893676a/transformed/viewpager2-1.0.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager2:viewpager2:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/0a985a726a0ed91053e59647f893676a/transformed/viewpager2-1.0.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.6.1] /Users/<USER>/.gradle/caches/8.13/transforms/7cf11ab6a97ce7a19d873c7fc9f6245f/transformed/fragment-1.6.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment:1.6.1] /Users/<USER>/.gradle/caches/8.13/transforms/7cf11ab6a97ce7a19d873c7fc9f6245f/transformed/fragment-1.6.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.8.0] /Users/<USER>/.gradle/caches/8.13/transforms/8f54346d7887f87b0ac14941a641e1e5/transformed/activity-1.8.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.8.0] /Users/<USER>/.gradle/caches/8.13/transforms/8f54346d7887f87b0ac14941a641e1e5/transformed/activity-1.8.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity-ktx:1.8.0] /Users/<USER>/.gradle/caches/8.13/transforms/7e03b29ff010dcda13836cde37647491/transformed/activity-ktx-1.8.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.8.0] /Users/<USER>/.gradle/caches/8.13/transforms/7e03b29ff010dcda13836cde37647491/transformed/activity-ktx-1.8.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] /Users/<USER>/.gradle/caches/8.13/transforms/86cbd6534acf09b6edb34f1ee4cf1ea6/transformed/savedstate-ktx-1.2.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] /Users/<USER>/.gradle/caches/8.13/transforms/86cbd6534acf09b6edb34f1ee4cf1ea6/transformed/savedstate-ktx-1.2.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/7cbd0f7d2422a506820c11fbac42769d/transformed/legacy-support-core-ui-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/7cbd0f7d2422a506820c11fbac42769d/transformed/legacy-support-core-ui-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/675643d2417f53f62de6e71cb70c4a03/transformed/coordinatorlayout-1.2.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/675643d2417f53f62de6e71cb70c4a03/transformed/coordinatorlayout-1.2.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.1.0] /Users/<USER>/.gradle/caches/8.13/transforms/a803a10f06d03b85c791e9e7827df3a7/transformed/swiperefreshlayout-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.1.0] /Users/<USER>/.gradle/caches/8.13/transforms/a803a10f06d03b85c791e9e7827df3a7/transformed/swiperefreshlayout-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.webkit:webkit:1.4.0] /Users/<USER>/.gradle/caches/8.13/transforms/c7cd1ae60e32cf15d1d605bbf04384dc/transformed/webkit-1.4.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.webkit:webkit:1.4.0] /Users/<USER>/.gradle/caches/8.13/transforms/c7cd1ae60e32cf15d1d605bbf04384dc/transformed/webkit-1.4.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.1.0] /Users/<USER>/.gradle/caches/8.13/transforms/6126635189ee6e9915c55ed159ffcefd/transformed/autofill-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.1.0] /Users/<USER>/.gradle/caches/8.13/transforms/6126635189ee6e9915c55ed159ffcefd/transformed/autofill-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [com.facebook.fresco:animated-gif:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/2edb4108b6d3af54f55ac8f7a3fd9ae0/transformed/animated-gif-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:animated-gif:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/2edb4108b6d3af54f55ac8f7a3fd9ae0/transformed/animated-gif-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:webpsupport:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/0297df8fcc2974bf1a6749148dbc26c0/transformed/webpsupport-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:webpsupport:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/0297df8fcc2974bf1a6749148dbc26c0/transformed/webpsupport-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:fresco:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/9ace98e86e8daf77252a03f707fe16e5/transformed/fresco-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:fresco:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/9ace98e86e8daf77252a03f707fe16e5/transformed/fresco-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-okhttp3:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/7c035c9f5baa4c873c913fa078c879e7/transformed/imagepipeline-okhttp3-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-okhttp3:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/7c035c9f5baa4c873c913fa078c879e7/transformed/imagepipeline-okhttp3-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:animated-base:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/6c0c3d9644f9203b6dc5a2f3ebd3236b/transformed/animated-base-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:animated-base:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/6c0c3d9644f9203b6dc5a2f3ebd3236b/transformed/animated-base-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:animated-drawable:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/1f4c51f0bcade3115e42ccfe51bd59a1/transformed/animated-drawable-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:animated-drawable:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/1f4c51f0bcade3115e42ccfe51bd59a1/transformed/animated-drawable-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:vito-options:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/ba9ef01bf112dd567fa66445cb5c61e2/transformed/vito-options-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:vito-options:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/ba9ef01bf112dd567fa66445cb5c61e2/transformed/vito-options-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:drawee:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/2f42596c4ca5d2009892461f9c7813b2/transformed/drawee-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:drawee:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/2f42596c4ca5d2009892461f9c7813b2/transformed/drawee-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:nativeimagefilters:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/4ba88b7da306cdab7c7db5ea63a68613/transformed/nativeimagefilters-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:nativeimagefilters:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/4ba88b7da306cdab7c7db5ea63a68613/transformed/nativeimagefilters-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-native:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/1d59e45c858309aed0d8a0127b93222a/transformed/memory-type-native-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-native:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/1d59e45c858309aed0d8a0127b93222a/transformed/memory-type-native-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-java:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/35c756a193f6db29047132ee07537a49/transformed/memory-type-java-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-java:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/35c756a193f6db29047132ee07537a49/transformed/memory-type-java-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-native:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/3fdf4f45aa1f8b9633b95505ee88a374/transformed/imagepipeline-native-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-native:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/3fdf4f45aa1f8b9633b95505ee88a374/transformed/imagepipeline-native-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-ashmem:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/a4e405a364064355f0d76d95d666e4c3/transformed/memory-type-ashmem-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-ashmem:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/a4e405a364064355f0d76d95d666e4c3/transformed/memory-type-ashmem-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/c8ab8ff0816e2da218676ecd718a2fdd/transformed/imagepipeline-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/c8ab8ff0816e2da218676ecd718a2fdd/transformed/imagepipeline-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:nativeimagetranscoder:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/c582b1007c5176013781b139f8986a2c/transformed/nativeimagetranscoder-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:nativeimagetranscoder:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/c582b1007c5176013781b139f8986a2c/transformed/nativeimagetranscoder-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-base:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/79f3259516df22757d258a93b0a50829/transformed/imagepipeline-base-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-base:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/79f3259516df22757d258a93b0a50829/transformed/imagepipeline-base-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:urimod:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/cde481b0d533be33fa0f770b40e08f80/transformed/urimod-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:urimod:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/cde481b0d533be33fa0f770b40e08f80/transformed/urimod-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:vito-source:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/da0dd16585cfbcbe69014c3bea92fdb6/transformed/vito-source-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:vito-source:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/da0dd16585cfbcbe69014c3bea92fdb6/transformed/vito-source-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:middleware:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/afcf702965a22ec9b05ff95daaacf5ab/transformed/middleware-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:middleware:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/afcf702965a22ec9b05ff95daaacf5ab/transformed/middleware-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:ui-common:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/2072985832aa083bee5cf76d9f79c99b/transformed/ui-common-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:ui-common:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/2072985832aa083bee5cf76d9f79c99b/transformed/ui-common-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:soloader:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/d2b07f954987ada6e44a53492ccc0edd/transformed/soloader-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:soloader:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/d2b07f954987ada6e44a53492ccc0edd/transformed/soloader-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:fbcore:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/805d64c1d3740b135c3c6d25d007a3e6/transformed/fbcore-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:fbcore:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/805d64c1d3740b135c3c6d25d007a3e6/transformed/fbcore-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.13.1] /Users/<USER>/.gradle/caches/8.13/transforms/44982b7d7b05239c12187164d2522afe/transformed/core-ktx-1.13.1/AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.13.1] /Users/<USER>/.gradle/caches/8.13/transforms/44982b7d7b05239c12187164d2522afe/transformed/core-ktx-1.13.1/AndroidManifest.xml:5:5-44
MERGED from [androidx.browser:browser:1.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/44d5193287b18dec49d9cd0bdd73b6e9/transformed/browser-1.6.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.browser:browser:1.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/44d5193287b18dec49d9cd0bdd73b6e9/transformed/browser-1.6.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.transition:transition:1.5.0] /Users/<USER>/.gradle/caches/8.13/transforms/bdbe8332f585138dd54b686bb64a82aa/transformed/transition-1.5.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.transition:transition:1.5.0] /Users/<USER>/.gradle/caches/8.13/transforms/bdbe8332f585138dd54b686bb64a82aa/transformed/transition-1.5.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] /Users/<USER>/.gradle/caches/8.13/transforms/eabbbcce580f93f46e8da890fc2dc6b7/transformed/drawerlayout-1.1.1/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] /Users/<USER>/.gradle/caches/8.13/transforms/eabbbcce580f93f46e8da890fc2dc6b7/transformed/drawerlayout-1.1.1/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/ce19d4bbc6c73debd3675c7709924648/transformed/dynamicanimation-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/ce19d4bbc6c73debd3675c7709924648/transformed/dynamicanimation-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] /Users/<USER>/.gradle/caches/8.13/transforms/814e25e87c08337205c573e8fbde7912/transformed/vectordrawable-animated-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] /Users/<USER>/.gradle/caches/8.13/transforms/814e25e87c08337205c573e8fbde7912/transformed/vectordrawable-animated-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] /Users/<USER>/.gradle/caches/8.13/transforms/d506d84aa1481d9d5e3867a8b5a87066/transformed/vectordrawable-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] /Users/<USER>/.gradle/caches/8.13/transforms/d506d84aa1481d9d5e3867a8b5a87066/transformed/vectordrawable-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/8109800e0431b448d21295476395ef1d/transformed/emoji2-views-helper-1.3.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/8109800e0431b448d21295476395ef1d/transformed/emoji2-views-helper-1.3.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/bdcbec29e8d0b717843f3dbae0b94f84/transformed/emoji2-1.3.0/AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/bdcbec29e8d0b717843f3dbae0b94f84/transformed/emoji2-1.3.0/AndroidManifest.xml:21:5-44
MERGED from [androidx.media:media:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/17e95e9d548b080f2342fe4e9d66b27c/transformed/media-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.media:media:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/17e95e9d548b080f2342fe4e9d66b27c/transformed/media-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/919c29d3ded19ccee428cb72162a56a5/transformed/legacy-support-core-utils-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/919c29d3ded19ccee428cb72162a56a5/transformed/legacy-support-core-utils-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/7ed80d3bf5b670828b2b3bc6f4b4dfeb/transformed/loader-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/7ed80d3bf5b670828b2b3bc6f4b4dfeb/transformed/loader-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/19e333d2a1598d1596ab4253be890ecc/transformed/viewpager-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/19e333d2a1598d1596ab4253be890ecc/transformed/viewpager-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.recyclerview:recyclerview:1.1.0] /Users/<USER>/.gradle/caches/8.13/transforms/47a56a4f1be0e9a7d815855bffa6881d/transformed/recyclerview-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.recyclerview:recyclerview:1.1.0] /Users/<USER>/.gradle/caches/8.13/transforms/47a56a4f1be0e9a7d815855bffa6881d/transformed/recyclerview-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/bf22f6268219602ea588d58cca050520/transformed/slidingpanelayout-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/bf22f6268219602ea588d58cca050520/transformed/slidingpanelayout-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.1.0] /Users/<USER>/.gradle/caches/8.13/transforms/2876b5b071ba525c32ef673b01793303/transformed/customview-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] /Users/<USER>/.gradle/caches/8.13/transforms/2876b5b071ba525c32ef673b01793303/transformed/customview-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/9fe9a9a5dc989223350b29a84763d980/transformed/asynclayoutinflater-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/9fe9a9a5dc989223350b29a84763d980/transformed/asynclayoutinflater-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.13/transforms/25ad4bdeddd79d498a0e9dfe4af5a7d2/transformed/core-1.13.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.13/transforms/25ad4bdeddd79d498a0e9dfe4af5a7d2/transformed/core-1.13.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/6a7fec56ee66dd7f7a0a2b21f81fa72c/transformed/lifecycle-viewmodel-ktx-2.6.2/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/6a7fec56ee66dd7f7a0a2b21f81fa72c/transformed/lifecycle-viewmodel-ktx-2.6.2/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/bae0062137d08dab4b3c13dc4624a335/transformed/lifecycle-viewmodel-2.6.2/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/bae0062137d08dab4b3c13dc4624a335/transformed/lifecycle-viewmodel-2.6.2/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/fece13e9c08c13338369de69cd14e88c/transformed/lifecycle-runtime-ktx-2.6.2/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/fece13e9c08c13338369de69cd14e88c/transformed/lifecycle-runtime-ktx-2.6.2/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/ec3f62d4745054bc29342c5607bcde4d/transformed/lifecycle-process-2.6.2/AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/ec3f62d4745054bc29342c5607bcde4d/transformed/lifecycle-process-2.6.2/AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/10b213ecea0e4e52da532b6ba0f83370/transformed/lifecycle-livedata-core-ktx-2.6.2/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/10b213ecea0e4e52da532b6ba0f83370/transformed/lifecycle-livedata-core-ktx-2.6.2/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/74c5ef7ab2dc463f805db9e9370b6b3a/transformed/lifecycle-livedata-core-2.6.2/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/74c5ef7ab2dc463f805db9e9370b6b3a/transformed/lifecycle-livedata-core-2.6.2/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/c0f61044d9766c534145df491b529aab/transformed/lifecycle-livedata-2.6.2/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/c0f61044d9766c534145df491b529aab/transformed/lifecycle-livedata-2.6.2/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/fb72dc500fcacad8fdd089fb6045d7e1/transformed/lifecycle-runtime-2.6.2/AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/fb72dc500fcacad8fdd089fb6045d7e1/transformed/lifecycle-runtime-2.6.2/AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/b6d7366c2eeb6fe782cd6baab9130943/transformed/lifecycle-viewmodel-savedstate-2.6.2/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/b6d7366c2eeb6fe782cd6baab9130943/transformed/lifecycle-viewmodel-savedstate-2.6.2/AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] /Users/<USER>/.gradle/caches/8.13/transforms/0b7ca141aa9d3a05c286852a98884a1f/transformed/savedstate-1.2.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] /Users/<USER>/.gradle/caches/8.13/transforms/0b7ca141aa9d3a05c286852a98884a1f/transformed/savedstate-1.2.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/3070f604e3c80036d56e6e100694a483/transformed/profileinstaller-1.3.1/AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/3070f604e3c80036d56e6e100694a483/transformed/profileinstaller-1.3.1/AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.13/transforms/b02b404f32dd5de34e82e545d50d8192/transformed/startup-runtime-1.1.1/AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.13/transforms/b02b404f32dd5de34e82e545d50d8192/transformed/startup-runtime-1.1.1/AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/eb45ff10c0db144c5a5537429e93efe3/transformed/tracing-1.2.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing:1.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/eb45ff10c0db144c5a5537429e93efe3/transformed/tracing-1.2.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing-ktx:1.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/1bbf8401d34e5d62926e60ab182c1f96/transformed/tracing-ktx-1.2.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing-ktx:1.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/1bbf8401d34e5d62926e60ab182c1f96/transformed/tracing-ktx-1.2.0/AndroidManifest.xml:20:5-44
MERGED from [com.facebook.fresco:ui-core:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/be04f8773d381722a4b4262f7c46e102/transformed/ui-core-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:ui-core:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/be04f8773d381722a4b4262f7c46e102/transformed/ui-core-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.0] /Users/<USER>/.gradle/caches/8.13/transforms/9a3668def9ff4e9f17f553dcb425dac8/transformed/annotation-experimental-1.4.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.0] /Users/<USER>/.gradle/caches/8.13/transforms/9a3668def9ff4e9f17f553dcb425dac8/transformed/annotation-experimental-1.4.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:vito-renderer:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/52f3a2fa8c0df5a8414fb52db1dd2780/transformed/vito-renderer-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:vito-renderer:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/52f3a2fa8c0df5a8414fb52db1dd2780/transformed/vito-renderer-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.react:hermes-android:0.79.4] /Users/<USER>/.gradle/caches/8.13/transforms/08e098abbe4d9b976b042791d1d5adf2/transformed/hermes-android-0.79.4-debug/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.react:hermes-android:0.79.4] /Users/<USER>/.gradle/caches/8.13/transforms/08e098abbe4d9b976b042791d1d5adf2/transformed/hermes-android-0.79.4-debug/AndroidManifest.xml:5:5-44
MERGED from [com.github.Dimezis:BlurView:version-2.0.6] /Users/<USER>/.gradle/caches/8.13/transforms/67a76227458514088161a2b0747e4617/transformed/BlurView-version-2.0.6/AndroidManifest.xml:5:5-7:41
MERGED from [com.github.Dimezis:BlurView:version-2.0.6] /Users/<USER>/.gradle/caches/8.13/transforms/67a76227458514088161a2b0747e4617/transformed/BlurView-version-2.0.6/AndroidManifest.xml:5:5-7:41
MERGED from [androidx.interpolator:interpolator:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/b0dc910de971c283f6356d856b6fba5e/transformed/interpolator-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/b0dc910de971c283f6356d856b6fba5e/transformed/interpolator-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/57b0ed4888c5d35f21d94e7e07d01862/transformed/cardview-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/57b0ed4888c5d35f21d94e7e07d01862/transformed/cardview-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/5c242fbce19fdaa380f4f5131b50e6e6/transformed/cursoradapter-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/5c242fbce19fdaa380f4f5131b50e6e6/transformed/cursoradapter-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [com.github.bumptech.glide:gifdecoder:4.16.0] /Users/<USER>/.gradle/caches/8.13/transforms/e78d9459b2e333fa78bced9c54bd129f/transformed/gifdecoder-4.16.0/AndroidManifest.xml:5:5-7:41
MERGED from [com.github.bumptech.glide:gifdecoder:4.16.0] /Users/<USER>/.gradle/caches/8.13/transforms/e78d9459b2e333fa78bced9c54bd129f/transformed/gifdecoder-4.16.0/AndroidManifest.xml:5:5-7:41
MERGED from [androidx.exifinterface:exifinterface:1.3.6] /Users/<USER>/.gradle/caches/8.13/transforms/436799b26b86e567b064386e20bb09aa/transformed/exifinterface-1.3.6/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.exifinterface:exifinterface:1.3.6] /Users/<USER>/.gradle/caches/8.13/transforms/436799b26b86e567b064386e20bb09aa/transformed/exifinterface-1.3.6/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/8.13/transforms/be6898bf4d583734f3f30b86f3aa7db4/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/8.13/transforms/be6898bf4d583734f3f30b86f3aa7db4/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.documentfile:documentfile:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/7c602fb14d62922ac8d7f7c4ab0d51ca/transformed/documentfile-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/7c602fb14d62922ac8d7f7c4ab0d51ca/transformed/documentfile-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/efff7a0c1ff905ae9950c94d7858e693/transformed/localbroadcastmanager-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/efff7a0c1ff905ae9950c94d7858e693/transformed/localbroadcastmanager-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/adf446957242012738eebe9b97a609ca/transformed/print-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/adf446957242012738eebe9b97a609ca/transformed/print-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/fcdbde368ff3f7b130f131373693d72e/transformed/core-runtime-2.2.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/fcdbde368ff3f7b130f131373693d72e/transformed/core-runtime-2.2.0/AndroidManifest.xml:20:5-44
MERGED from [com.facebook.fbjni:fbjni:0.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/f7366e53586a12172ab374397325a4bf/transformed/fbjni-0.7.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fbjni:fbjni:0.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/f7366e53586a12172ab374397325a4bf/transformed/fbjni-0.7.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.soloader:soloader:0.12.1] /Users/<USER>/.gradle/caches/8.13/transforms/fdb63001aee1d718e95ecb2b3adfa162/transformed/soloader-0.12.1/AndroidManifest.xml:7:5-9:41
MERGED from [com.facebook.soloader:soloader:0.12.1] /Users/<USER>/.gradle/caches/8.13/transforms/fdb63001aee1d718e95ecb2b3adfa162/transformed/soloader-0.12.1/AndroidManifest.xml:7:5-9:41
MERGED from [com.caverock:androidsvg-aar:1.4] /Users/<USER>/.gradle/caches/8.13/transforms/0ced2c618cc0851d3ee11456ab9ae13d/transformed/androidsvg-aar-1.4/AndroidManifest.xml:7:5-9:41
MERGED from [com.caverock:androidsvg-aar:1.4] /Users/<USER>/.gradle/caches/8.13/transforms/0ced2c618cc0851d3ee11456ab9ae13d/transformed/androidsvg-aar-1.4/AndroidManifest.xml:7:5-9:41
MERGED from [org.aomedia.avif.android:avif:1.0.1.262e11d] /Users/<USER>/.gradle/caches/8.13/transforms/fd97a93c331c36d4de146222ebe73ebb/transformed/avif-1.0.1.262e11d/AndroidManifest.xml:4:5-44
MERGED from [org.aomedia.avif.android:avif:1.0.1.262e11d] /Users/<USER>/.gradle/caches/8.13/transforms/fd97a93c331c36d4de146222ebe73ebb/transformed/avif-1.0.1.262e11d/AndroidManifest.xml:4:5-44
	android:targetSdkVersion
		INJECTED from /Users/<USER>/not_work/my-app/android/app/src/debug/AndroidManifest.xml
	android:minSdkVersion
		INJECTED from /Users/<USER>/not_work/my-app/android/app/src/debug/AndroidManifest.xml
provider#com.reactnativecommunity.webview.RNCWebViewFileProvider
ADDED from [:react-native-webview] /Users/<USER>/not_work/my-app/node_modules/react-native-webview/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:8:9-16:20
	android:grantUriPermissions
		ADDED from [:react-native-webview] /Users/<USER>/not_work/my-app/node_modules/react-native-webview/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:12:13-47
	android:authorities
		ADDED from [:react-native-webview] /Users/<USER>/not_work/my-app/node_modules/react-native-webview/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:10:13-64
	android:exported
		ADDED from [:react-native-webview] /Users/<USER>/not_work/my-app/node_modules/react-native-webview/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:11:13-37
	android:name
		ADDED from [:react-native-webview] /Users/<USER>/not_work/my-app/node_modules/react-native-webview/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:9:13-83
meta-data#android.support.FILE_PROVIDER_PATHS
ADDED from [:react-native-webview] /Users/<USER>/not_work/my-app/node_modules/react-native-webview/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:13:13-15:63
	android:resource
		ADDED from [:react-native-webview] /Users/<USER>/not_work/my-app/node_modules/react-native-webview/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:15:17-60
	android:name
		ADDED from [:react-native-webview] /Users/<USER>/not_work/my-app/node_modules/react-native-webview/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:14:17-67
meta-data#org.unimodules.core.AppLoader#react-native-headless
ADDED from [:expo-modules-core] /Users/<USER>/not_work/my-app/node_modules/expo-modules-core/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:9:9-11:89
	android:value
		ADDED from [:expo-modules-core] /Users/<USER>/not_work/my-app/node_modules/expo-modules-core/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:11:13-86
	android:name
		ADDED from [:expo-modules-core] /Users/<USER>/not_work/my-app/node_modules/expo-modules-core/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:10:13-79
meta-data#com.facebook.soloader.enabled
ADDED from [:expo-modules-core] /Users/<USER>/not_work/my-app/node_modules/expo-modules-core/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:12:9-15:45
MERGED from [com.facebook.soloader:soloader:0.12.1] /Users/<USER>/.gradle/caches/8.13/transforms/fdb63001aee1d718e95ecb2b3adfa162/transformed/soloader-0.12.1/AndroidManifest.xml:12:9-14:37
MERGED from [com.facebook.soloader:soloader:0.12.1] /Users/<USER>/.gradle/caches/8.13/transforms/fdb63001aee1d718e95ecb2b3adfa162/transformed/soloader-0.12.1/AndroidManifest.xml:12:9-14:37
	tools:replace
		ADDED from [:expo-modules-core] /Users/<USER>/not_work/my-app/node_modules/expo-modules-core/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:15:13-42
	android:value
		ADDED from [:expo-modules-core] /Users/<USER>/not_work/my-app/node_modules/expo-modules-core/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:14:13-33
		REJECTED from [com.facebook.soloader:soloader:0.12.1] /Users/<USER>/.gradle/caches/8.13/transforms/fdb63001aee1d718e95ecb2b3adfa162/transformed/soloader-0.12.1/AndroidManifest.xml:14:13-34
	android:name
		ADDED from [:expo-modules-core] /Users/<USER>/not_work/my-app/node_modules/expo-modules-core/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:13:13-57
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from [BareExpo:expo.modules.image:2.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/3436d2bb0e66cba9430428ecbc7fb171/transformed/expo.modules.image-2.3.0/AndroidManifest.xml:12:5-79
	android:name
		ADDED from [BareExpo:expo.modules.image:2.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/3436d2bb0e66cba9430428ecbc7fb171/transformed/expo.modules.image-2.3.0/AndroidManifest.xml:12:22-76
activity#com.facebook.react.devsupport.DevSettingsActivity
ADDED from [com.facebook.react:react-android:0.79.4] /Users/<USER>/.gradle/caches/8.13/transforms/ee3752888ecf2d82fe4920d23f93a394/transformed/react-android-0.79.4-debug/AndroidManifest.xml:19:9-21:40
	android:exported
		ADDED from [com.facebook.react:react-android:0.79.4] /Users/<USER>/.gradle/caches/8.13/transforms/ee3752888ecf2d82fe4920d23f93a394/transformed/react-android-0.79.4-debug/AndroidManifest.xml:21:13-37
	android:name
		ADDED from [com.facebook.react:react-android:0.79.4] /Users/<USER>/.gradle/caches/8.13/transforms/ee3752888ecf2d82fe4920d23f93a394/transformed/react-android-0.79.4-debug/AndroidManifest.xml:20:13-77
intent#action:name:android.intent.action.OPEN_DOCUMENT_TREE
ADDED from [:expo-file-system] /Users/<USER>/not_work/my-app/node_modules/expo-file-system/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:15:9-17:18
action#android.intent.action.OPEN_DOCUMENT_TREE
ADDED from [:expo-file-system] /Users/<USER>/not_work/my-app/node_modules/expo-file-system/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:16:13-79
	android:name
		ADDED from [:expo-file-system] /Users/<USER>/not_work/my-app/node_modules/expo-file-system/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:16:21-76
provider#expo.modules.filesystem.FileSystemFileProvider
ADDED from [:expo-file-system] /Users/<USER>/not_work/my-app/node_modules/expo-file-system/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:21:9-30:20
	android:grantUriPermissions
		ADDED from [:expo-file-system] /Users/<USER>/not_work/my-app/node_modules/expo-file-system/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:25:13-47
	android:authorities
		ADDED from [:expo-file-system] /Users/<USER>/not_work/my-app/node_modules/expo-file-system/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:23:13-74
	android:exported
		ADDED from [:expo-file-system] /Users/<USER>/not_work/my-app/node_modules/expo-file-system/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:24:13-37
	tools:replace
		ADDED from [:expo-file-system] /Users/<USER>/not_work/my-app/node_modules/expo-file-system/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:26:13-48
	android:name
		ADDED from [:expo-file-system] /Users/<USER>/not_work/my-app/node_modules/expo-file-system/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:22:13-74
intent#action:name:android.support.customtabs.action.CustomTabsService
ADDED from [host.exp.exponent:expo.modules.webbrowser:14.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/b2e62f529b3dc5334aec2c40acace88a/transformed/expo.modules.webbrowser-14.2.0/AndroidManifest.xml:8:9-12:18
action#android.support.customtabs.action.CustomTabsService
ADDED from [host.exp.exponent:expo.modules.webbrowser:14.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/b2e62f529b3dc5334aec2c40acace88a/transformed/expo.modules.webbrowser-14.2.0/AndroidManifest.xml:11:13-90
	android:name
		ADDED from [host.exp.exponent:expo.modules.webbrowser:14.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/b2e62f529b3dc5334aec2c40acace88a/transformed/expo.modules.webbrowser-14.2.0/AndroidManifest.xml:11:21-87
meta-data#com.bumptech.glide.integration.okhttp3.OkHttpGlideModule
ADDED from [com.github.bumptech.glide:okhttp3-integration:4.11.0] /Users/<USER>/.gradle/caches/8.13/transforms/20c2c99566bca8247160f1fa1ef8c1cc/transformed/okhttp3-integration-4.11.0/AndroidManifest.xml:11:9-13:43
	android:value
		ADDED from [com.github.bumptech.glide:okhttp3-integration:4.11.0] /Users/<USER>/.gradle/caches/8.13/transforms/20c2c99566bca8247160f1fa1ef8c1cc/transformed/okhttp3-integration-4.11.0/AndroidManifest.xml:13:13-40
	android:name
		ADDED from [com.github.bumptech.glide:okhttp3-integration:4.11.0] /Users/<USER>/.gradle/caches/8.13/transforms/20c2c99566bca8247160f1fa1ef8c1cc/transformed/okhttp3-integration-4.11.0/AndroidManifest.xml:12:13-84
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/bdcbec29e8d0b717843f3dbae0b94f84/transformed/emoji2-1.3.0/AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/ec3f62d4745054bc29342c5607bcde4d/transformed/lifecycle-process-2.6.2/AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/ec3f62d4745054bc29342c5607bcde4d/transformed/lifecycle-process-2.6.2/AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/3070f604e3c80036d56e6e100694a483/transformed/profileinstaller-1.3.1/AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/3070f604e3c80036d56e6e100694a483/transformed/profileinstaller-1.3.1/AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.13/transforms/b02b404f32dd5de34e82e545d50d8192/transformed/startup-runtime-1.1.1/AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.13/transforms/b02b404f32dd5de34e82e545d50d8192/transformed/startup-runtime-1.1.1/AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/bdcbec29e8d0b717843f3dbae0b94f84/transformed/emoji2-1.3.0/AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/bdcbec29e8d0b717843f3dbae0b94f84/transformed/emoji2-1.3.0/AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/bdcbec29e8d0b717843f3dbae0b94f84/transformed/emoji2-1.3.0/AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/bdcbec29e8d0b717843f3dbae0b94f84/transformed/emoji2-1.3.0/AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/bdcbec29e8d0b717843f3dbae0b94f84/transformed/emoji2-1.3.0/AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/bdcbec29e8d0b717843f3dbae0b94f84/transformed/emoji2-1.3.0/AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/bdcbec29e8d0b717843f3dbae0b94f84/transformed/emoji2-1.3.0/AndroidManifest.xml:30:17-75
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.13/transforms/25ad4bdeddd79d498a0e9dfe4af5a7d2/transformed/core-1.13.1/AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.13/transforms/25ad4bdeddd79d498a0e9dfe4af5a7d2/transformed/core-1.13.1/AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.13/transforms/25ad4bdeddd79d498a0e9dfe4af5a7d2/transformed/core-1.13.1/AndroidManifest.xml:23:9-81
permission#com.anonymous.myapp.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.13/transforms/25ad4bdeddd79d498a0e9dfe4af5a7d2/transformed/core-1.13.1/AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.13/transforms/25ad4bdeddd79d498a0e9dfe4af5a7d2/transformed/core-1.13.1/AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.13/transforms/25ad4bdeddd79d498a0e9dfe4af5a7d2/transformed/core-1.13.1/AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.13/transforms/25ad4bdeddd79d498a0e9dfe4af5a7d2/transformed/core-1.13.1/AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.13/transforms/25ad4bdeddd79d498a0e9dfe4af5a7d2/transformed/core-1.13.1/AndroidManifest.xml:26:22-94
uses-permission#com.anonymous.myapp.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.13/transforms/25ad4bdeddd79d498a0e9dfe4af5a7d2/transformed/core-1.13.1/AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.13/transforms/25ad4bdeddd79d498a0e9dfe4af5a7d2/transformed/core-1.13.1/AndroidManifest.xml:26:22-94
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/ec3f62d4745054bc29342c5607bcde4d/transformed/lifecycle-process-2.6.2/AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/ec3f62d4745054bc29342c5607bcde4d/transformed/lifecycle-process-2.6.2/AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/ec3f62d4745054bc29342c5607bcde4d/transformed/lifecycle-process-2.6.2/AndroidManifest.xml:30:17-78
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/3070f604e3c80036d56e6e100694a483/transformed/profileinstaller-1.3.1/AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/3070f604e3c80036d56e6e100694a483/transformed/profileinstaller-1.3.1/AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/3070f604e3c80036d56e6e100694a483/transformed/profileinstaller-1.3.1/AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/3070f604e3c80036d56e6e100694a483/transformed/profileinstaller-1.3.1/AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/3070f604e3c80036d56e6e100694a483/transformed/profileinstaller-1.3.1/AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/3070f604e3c80036d56e6e100694a483/transformed/profileinstaller-1.3.1/AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/3070f604e3c80036d56e6e100694a483/transformed/profileinstaller-1.3.1/AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/3070f604e3c80036d56e6e100694a483/transformed/profileinstaller-1.3.1/AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/3070f604e3c80036d56e6e100694a483/transformed/profileinstaller-1.3.1/AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/3070f604e3c80036d56e6e100694a483/transformed/profileinstaller-1.3.1/AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/3070f604e3c80036d56e6e100694a483/transformed/profileinstaller-1.3.1/AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/3070f604e3c80036d56e6e100694a483/transformed/profileinstaller-1.3.1/AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/3070f604e3c80036d56e6e100694a483/transformed/profileinstaller-1.3.1/AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/3070f604e3c80036d56e6e100694a483/transformed/profileinstaller-1.3.1/AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/3070f604e3c80036d56e6e100694a483/transformed/profileinstaller-1.3.1/AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/3070f604e3c80036d56e6e100694a483/transformed/profileinstaller-1.3.1/AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/3070f604e3c80036d56e6e100694a483/transformed/profileinstaller-1.3.1/AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/3070f604e3c80036d56e6e100694a483/transformed/profileinstaller-1.3.1/AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/3070f604e3c80036d56e6e100694a483/transformed/profileinstaller-1.3.1/AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/3070f604e3c80036d56e6e100694a483/transformed/profileinstaller-1.3.1/AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/3070f604e3c80036d56e6e100694a483/transformed/profileinstaller-1.3.1/AndroidManifest.xml:50:25-92
