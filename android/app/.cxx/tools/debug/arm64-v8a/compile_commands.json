[{"directory": "/Users/<USER>/not_work/my-app/android/app/.cxx/Debug/682h5i28/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -Dappmodules_EXPORTS -I/Users/<USER>/not_work/my-app/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup -I/Users/<USER>/not_work/my-app/android/app/build/generated/autolinking/src/main/jni -I/Users/<USER>/not_work/my-app/node_modules/react-native-safe-area-context/android/src/main/jni -I/Users/<USER>/not_work/my-app/node_modules/react-native-screens/android/src/main/jni -I/Users/<USER>/not_work/my-app/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/. -I/Users/<USER>/not_work/my-app/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen -I/Users/<USER>/not_work/my-app/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/. -I/Users/<USER>/not_work/my-app/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated -I/Users/<USER>/not_work/my-app/node_modules/react-native-safe-area-context/android/src/main/jni/. -I/Users/<USER>/not_work/my-app/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -I/Users/<USER>/not_work/my-app/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -I/Users/<USER>/not_work/my-app/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -I/Users/<USER>/not_work/my-app/node_modules/react-native-screens/android/src/main/jni/. -I/Users/<USER>/not_work/my-app/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -I/Users/<USER>/not_work/my-app/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -I/Users/<USER>/not_work/my-app/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -I/Users/<USER>/not_work/my-app/node_modules/react-native-webview/android/build/generated/source/codegen/jni/. -I/Users/<USER>/not_work/my-app/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec -I/Users/<USER>/not_work/my-app/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/. -I/Users/<USER>/not_work/my-app/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/react/renderer/components/RNEdgeToEdge -isystem /Users/<USER>/.gradle/caches/8.13/transforms/f7366e53586a12172ab374397325a4bf/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ee3752888ecf2d82fe4920d23f93a394/transformed/react-android-0.79.4-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ee3752888ecf2d82fe4920d23f93a394/transformed/react-android-0.79.4-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -Wall -Werror -Wno-error=cpp -fexceptions -frtti -std=c++20 -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o CMakeFiles/appmodules.dir/Users/<USER>/not_work/my-app/android/app/build/generated/autolinking/src/main/jni/autolinking.cpp.o -c /Users/<USER>/not_work/my-app/android/app/build/generated/autolinking/src/main/jni/autolinking.cpp", "file": "/Users/<USER>/not_work/my-app/android/app/build/generated/autolinking/src/main/jni/autolinking.cpp"}, {"directory": "/Users/<USER>/not_work/my-app/android/app/.cxx/Debug/682h5i28/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -Dappmodules_EXPORTS -I/Users/<USER>/not_work/my-app/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup -I/Users/<USER>/not_work/my-app/android/app/build/generated/autolinking/src/main/jni -I/Users/<USER>/not_work/my-app/node_modules/react-native-safe-area-context/android/src/main/jni -I/Users/<USER>/not_work/my-app/node_modules/react-native-screens/android/src/main/jni -I/Users/<USER>/not_work/my-app/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/. -I/Users/<USER>/not_work/my-app/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen -I/Users/<USER>/not_work/my-app/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/. -I/Users/<USER>/not_work/my-app/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated -I/Users/<USER>/not_work/my-app/node_modules/react-native-safe-area-context/android/src/main/jni/. -I/Users/<USER>/not_work/my-app/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -I/Users/<USER>/not_work/my-app/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -I/Users/<USER>/not_work/my-app/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -I/Users/<USER>/not_work/my-app/node_modules/react-native-screens/android/src/main/jni/. -I/Users/<USER>/not_work/my-app/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -I/Users/<USER>/not_work/my-app/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -I/Users/<USER>/not_work/my-app/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -I/Users/<USER>/not_work/my-app/node_modules/react-native-webview/android/build/generated/source/codegen/jni/. -I/Users/<USER>/not_work/my-app/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec -I/Users/<USER>/not_work/my-app/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/. -I/Users/<USER>/not_work/my-app/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/react/renderer/components/RNEdgeToEdge -isystem /Users/<USER>/.gradle/caches/8.13/transforms/f7366e53586a12172ab374397325a4bf/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ee3752888ecf2d82fe4920d23f93a394/transformed/react-android-0.79.4-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ee3752888ecf2d82fe4920d23f93a394/transformed/react-android-0.79.4-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -Wall -Werror -Wno-error=cpp -fexceptions -frtti -std=c++20 -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o CMakeFiles/appmodules.dir/OnLoad.cpp.o -c /Users/<USER>/not_work/my-app/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup/OnLoad.cpp", "file": "/Users/<USER>/not_work/my-app/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup/OnLoad.cpp"}, {"directory": "/Users/<USER>/not_work/my-app/android/app/.cxx/Debug/682h5i28/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -I/Users/<USER>/not_work/my-app/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/. -I/Users/<USER>/not_work/my-app/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen -isystem /Users/<USER>/.gradle/caches/8.13/transforms/f7366e53586a12172ab374397325a4bf/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ee3752888ecf2d82fe4920d23f93a394/transformed/react-android-0.79.4-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ee3752888ecf2d82fe4920d23f93a394/transformed/react-android-0.79.4-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/ComponentDescriptors.cpp.o -c /Users/<USER>/not_work/my-app/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen/ComponentDescriptors.cpp", "file": "/Users/<USER>/not_work/my-app/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen/ComponentDescriptors.cpp"}, {"directory": "/Users/<USER>/not_work/my-app/android/app/.cxx/Debug/682h5i28/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -I/Users/<USER>/not_work/my-app/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/. -I/Users/<USER>/not_work/my-app/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen -isystem /Users/<USER>/.gradle/caches/8.13/transforms/f7366e53586a12172ab374397325a4bf/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ee3752888ecf2d82fe4920d23f93a394/transformed/react-android-0.79.4-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ee3752888ecf2d82fe4920d23f93a394/transformed/react-android-0.79.4-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/EventEmitters.cpp.o -c /Users/<USER>/not_work/my-app/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen/EventEmitters.cpp", "file": "/Users/<USER>/not_work/my-app/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen/EventEmitters.cpp"}, {"directory": "/Users/<USER>/not_work/my-app/android/app/.cxx/Debug/682h5i28/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -I/Users/<USER>/not_work/my-app/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/. -I/Users/<USER>/not_work/my-app/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen -isystem /Users/<USER>/.gradle/caches/8.13/transforms/f7366e53586a12172ab374397325a4bf/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ee3752888ecf2d82fe4920d23f93a394/transformed/react-android-0.79.4-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ee3752888ecf2d82fe4920d23f93a394/transformed/react-android-0.79.4-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/Props.cpp.o -c /Users/<USER>/not_work/my-app/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen/Props.cpp", "file": "/Users/<USER>/not_work/my-app/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen/Props.cpp"}, {"directory": "/Users/<USER>/not_work/my-app/android/app/.cxx/Debug/682h5i28/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -I/Users/<USER>/not_work/my-app/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/. -I/Users/<USER>/not_work/my-app/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen -isystem /Users/<USER>/.gradle/caches/8.13/transforms/f7366e53586a12172ab374397325a4bf/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ee3752888ecf2d82fe4920d23f93a394/transformed/react-android-0.79.4-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ee3752888ecf2d82fe4920d23f93a394/transformed/react-android-0.79.4-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/ShadowNodes.cpp.o -c /Users/<USER>/not_work/my-app/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen/ShadowNodes.cpp", "file": "/Users/<USER>/not_work/my-app/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen/ShadowNodes.cpp"}, {"directory": "/Users/<USER>/not_work/my-app/android/app/.cxx/Debug/682h5i28/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -I/Users/<USER>/not_work/my-app/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/. -I/Users/<USER>/not_work/my-app/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen -isystem /Users/<USER>/.gradle/caches/8.13/transforms/f7366e53586a12172ab374397325a4bf/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ee3752888ecf2d82fe4920d23f93a394/transformed/react-android-0.79.4-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ee3752888ecf2d82fe4920d23f93a394/transformed/react-android-0.79.4-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/States.cpp.o -c /Users/<USER>/not_work/my-app/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen/States.cpp", "file": "/Users/<USER>/not_work/my-app/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen/States.cpp"}, {"directory": "/Users/<USER>/not_work/my-app/android/app/.cxx/Debug/682h5i28/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -I/Users/<USER>/not_work/my-app/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/. -I/Users/<USER>/not_work/my-app/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen -isystem /Users/<USER>/.gradle/caches/8.13/transforms/f7366e53586a12172ab374397325a4bf/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ee3752888ecf2d82fe4920d23f93a394/transformed/react-android-0.79.4-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ee3752888ecf2d82fe4920d23f93a394/transformed/react-android-0.79.4-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/rngesturehandler_codegenJSI-generated.cpp.o -c /Users/<USER>/not_work/my-app/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen/rngesturehandler_codegenJSI-generated.cpp", "file": "/Users/<USER>/not_work/my-app/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen/rngesturehandler_codegenJSI-generated.cpp"}, {"directory": "/Users/<USER>/not_work/my-app/android/app/.cxx/Debug/682h5i28/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -I/Users/<USER>/not_work/my-app/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/. -I/Users/<USER>/not_work/my-app/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen -isystem /Users/<USER>/.gradle/caches/8.13/transforms/f7366e53586a12172ab374397325a4bf/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ee3752888ecf2d82fe4920d23f93a394/transformed/react-android-0.79.4-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ee3752888ecf2d82fe4920d23f93a394/transformed/react-android-0.79.4-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/rngesturehandler_codegen-generated.cpp.o -c /Users/<USER>/not_work/my-app/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/rngesturehandler_codegen-generated.cpp", "file": "/Users/<USER>/not_work/my-app/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/rngesturehandler_codegen-generated.cpp"}, {"directory": "/Users/<USER>/not_work/my-app/android/app/.cxx/Debug/682h5i28/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -I/Users/<USER>/not_work/my-app/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/. -I/Users/<USER>/not_work/my-app/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated -isystem /Users/<USER>/.gradle/caches/8.13/transforms/f7366e53586a12172ab374397325a4bf/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ee3752888ecf2d82fe4920d23f93a394/transformed/react-android-0.79.4-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ee3752888ecf2d82fe4920d23f93a394/transformed/react-android-0.79.4-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/ComponentDescriptors.cpp.o -c /Users/<USER>/not_work/my-app/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated/ComponentDescriptors.cpp", "file": "/Users/<USER>/not_work/my-app/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated/ComponentDescriptors.cpp"}, {"directory": "/Users/<USER>/not_work/my-app/android/app/.cxx/Debug/682h5i28/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -I/Users/<USER>/not_work/my-app/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/. -I/Users/<USER>/not_work/my-app/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated -isystem /Users/<USER>/.gradle/caches/8.13/transforms/f7366e53586a12172ab374397325a4bf/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ee3752888ecf2d82fe4920d23f93a394/transformed/react-android-0.79.4-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ee3752888ecf2d82fe4920d23f93a394/transformed/react-android-0.79.4-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/EventEmitters.cpp.o -c /Users/<USER>/not_work/my-app/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated/EventEmitters.cpp", "file": "/Users/<USER>/not_work/my-app/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated/EventEmitters.cpp"}, {"directory": "/Users/<USER>/not_work/my-app/android/app/.cxx/Debug/682h5i28/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -I/Users/<USER>/not_work/my-app/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/. -I/Users/<USER>/not_work/my-app/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated -isystem /Users/<USER>/.gradle/caches/8.13/transforms/f7366e53586a12172ab374397325a4bf/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ee3752888ecf2d82fe4920d23f93a394/transformed/react-android-0.79.4-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ee3752888ecf2d82fe4920d23f93a394/transformed/react-android-0.79.4-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/Props.cpp.o -c /Users/<USER>/not_work/my-app/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated/Props.cpp", "file": "/Users/<USER>/not_work/my-app/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated/Props.cpp"}, {"directory": "/Users/<USER>/not_work/my-app/android/app/.cxx/Debug/682h5i28/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -I/Users/<USER>/not_work/my-app/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/. -I/Users/<USER>/not_work/my-app/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated -isystem /Users/<USER>/.gradle/caches/8.13/transforms/f7366e53586a12172ab374397325a4bf/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ee3752888ecf2d82fe4920d23f93a394/transformed/react-android-0.79.4-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ee3752888ecf2d82fe4920d23f93a394/transformed/react-android-0.79.4-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/ShadowNodes.cpp.o -c /Users/<USER>/not_work/my-app/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated/ShadowNodes.cpp", "file": "/Users/<USER>/not_work/my-app/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated/ShadowNodes.cpp"}, {"directory": "/Users/<USER>/not_work/my-app/android/app/.cxx/Debug/682h5i28/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -I/Users/<USER>/not_work/my-app/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/. -I/Users/<USER>/not_work/my-app/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated -isystem /Users/<USER>/.gradle/caches/8.13/transforms/f7366e53586a12172ab374397325a4bf/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ee3752888ecf2d82fe4920d23f93a394/transformed/react-android-0.79.4-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ee3752888ecf2d82fe4920d23f93a394/transformed/react-android-0.79.4-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/States.cpp.o -c /Users/<USER>/not_work/my-app/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated/States.cpp", "file": "/Users/<USER>/not_work/my-app/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated/States.cpp"}, {"directory": "/Users/<USER>/not_work/my-app/android/app/.cxx/Debug/682h5i28/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -I/Users/<USER>/not_work/my-app/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/. -I/Users/<USER>/not_work/my-app/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated -isystem /Users/<USER>/.gradle/caches/8.13/transforms/f7366e53586a12172ab374397325a4bf/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ee3752888ecf2d82fe4920d23f93a394/transformed/react-android-0.79.4-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ee3752888ecf2d82fe4920d23f93a394/transformed/react-android-0.79.4-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/rnreanimatedJSI-generated.cpp.o -c /Users/<USER>/not_work/my-app/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated/rnreanimatedJSI-generated.cpp", "file": "/Users/<USER>/not_work/my-app/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated/rnreanimatedJSI-generated.cpp"}, {"directory": "/Users/<USER>/not_work/my-app/android/app/.cxx/Debug/682h5i28/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -I/Users/<USER>/not_work/my-app/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/. -I/Users/<USER>/not_work/my-app/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated -isystem /Users/<USER>/.gradle/caches/8.13/transforms/f7366e53586a12172ab374397325a4bf/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ee3752888ecf2d82fe4920d23f93a394/transformed/react-android-0.79.4-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ee3752888ecf2d82fe4920d23f93a394/transformed/react-android-0.79.4-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/rnreanimated-generated.cpp.o -c /Users/<USER>/not_work/my-app/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/rnreanimated-generated.cpp", "file": "/Users/<USER>/not_work/my-app/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/rnreanimated-generated.cpp"}, {"directory": "/Users/<USER>/not_work/my-app/android/app/.cxx/Debug/682h5i28/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -Dreact_codegen_safeareacontext_EXPORTS -I/Users/<USER>/not_work/my-app/node_modules/react-native-safe-area-context/android/src/main/jni/. -I/Users/<USER>/not_work/my-app/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -I/Users/<USER>/not_work/my-app/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -I/Users/<USER>/not_work/my-app/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem /Users/<USER>/.gradle/caches/8.13/transforms/f7366e53586a12172ab374397325a4bf/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ee3752888ecf2d82fe4920d23f93a394/transformed/react-android-0.79.4-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ee3752888ecf2d82fe4920d23f93a394/transformed/react-android-0.79.4-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/Users/<USER>/not_work/my-app/node_modules/react-native-safe-area-context/common/cpp/react/renderer/components/safeareacontext/RNCSafeAreaViewShadowNode.cpp.o -c /Users/<USER>/not_work/my-app/node_modules/react-native-safe-area-context/common/cpp/react/renderer/components/safeareacontext/RNCSafeAreaViewShadowNode.cpp", "file": "/Users/<USER>/not_work/my-app/node_modules/react-native-safe-area-context/common/cpp/react/renderer/components/safeareacontext/RNCSafeAreaViewShadowNode.cpp"}, {"directory": "/Users/<USER>/not_work/my-app/android/app/.cxx/Debug/682h5i28/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -Dreact_codegen_safeareacontext_EXPORTS -I/Users/<USER>/not_work/my-app/node_modules/react-native-safe-area-context/android/src/main/jni/. -I/Users/<USER>/not_work/my-app/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -I/Users/<USER>/not_work/my-app/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -I/Users/<USER>/not_work/my-app/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem /Users/<USER>/.gradle/caches/8.13/transforms/f7366e53586a12172ab374397325a4bf/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ee3752888ecf2d82fe4920d23f93a394/transformed/react-android-0.79.4-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ee3752888ecf2d82fe4920d23f93a394/transformed/react-android-0.79.4-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/Users/<USER>/not_work/my-app/node_modules/react-native-safe-area-context/common/cpp/react/renderer/components/safeareacontext/RNCSafeAreaViewState.cpp.o -c /Users/<USER>/not_work/my-app/node_modules/react-native-safe-area-context/common/cpp/react/renderer/components/safeareacontext/RNCSafeAreaViewState.cpp", "file": "/Users/<USER>/not_work/my-app/node_modules/react-native-safe-area-context/common/cpp/react/renderer/components/safeareacontext/RNCSafeAreaViewState.cpp"}, {"directory": "/Users/<USER>/not_work/my-app/android/app/.cxx/Debug/682h5i28/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -Dreact_codegen_safeareacontext_EXPORTS -I/Users/<USER>/not_work/my-app/node_modules/react-native-safe-area-context/android/src/main/jni/. -I/Users/<USER>/not_work/my-app/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -I/Users/<USER>/not_work/my-app/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -I/Users/<USER>/not_work/my-app/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem /Users/<USER>/.gradle/caches/8.13/transforms/f7366e53586a12172ab374397325a4bf/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ee3752888ecf2d82fe4920d23f93a394/transformed/react-android-0.79.4-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ee3752888ecf2d82fe4920d23f93a394/transformed/react-android-0.79.4-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/Users/<USER>/not_work/my-app/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/ComponentDescriptors.cpp.o -c /Users/<USER>/not_work/my-app/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/ComponentDescriptors.cpp", "file": "/Users/<USER>/not_work/my-app/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/ComponentDescriptors.cpp"}, {"directory": "/Users/<USER>/not_work/my-app/android/app/.cxx/Debug/682h5i28/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -Dreact_codegen_safeareacontext_EXPORTS -I/Users/<USER>/not_work/my-app/node_modules/react-native-safe-area-context/android/src/main/jni/. -I/Users/<USER>/not_work/my-app/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -I/Users/<USER>/not_work/my-app/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -I/Users/<USER>/not_work/my-app/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem /Users/<USER>/.gradle/caches/8.13/transforms/f7366e53586a12172ab374397325a4bf/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ee3752888ecf2d82fe4920d23f93a394/transformed/react-android-0.79.4-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ee3752888ecf2d82fe4920d23f93a394/transformed/react-android-0.79.4-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/Users/<USER>/not_work/my-app/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/EventEmitters.cpp.o -c /Users/<USER>/not_work/my-app/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/EventEmitters.cpp", "file": "/Users/<USER>/not_work/my-app/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/EventEmitters.cpp"}, {"directory": "/Users/<USER>/not_work/my-app/android/app/.cxx/Debug/682h5i28/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -Dreact_codegen_safeareacontext_EXPORTS -I/Users/<USER>/not_work/my-app/node_modules/react-native-safe-area-context/android/src/main/jni/. -I/Users/<USER>/not_work/my-app/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -I/Users/<USER>/not_work/my-app/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -I/Users/<USER>/not_work/my-app/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem /Users/<USER>/.gradle/caches/8.13/transforms/f7366e53586a12172ab374397325a4bf/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ee3752888ecf2d82fe4920d23f93a394/transformed/react-android-0.79.4-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ee3752888ecf2d82fe4920d23f93a394/transformed/react-android-0.79.4-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/Users/<USER>/not_work/my-app/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/Props.cpp.o -c /Users/<USER>/not_work/my-app/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/Props.cpp", "file": "/Users/<USER>/not_work/my-app/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/Props.cpp"}, {"directory": "/Users/<USER>/not_work/my-app/android/app/.cxx/Debug/682h5i28/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -Dreact_codegen_safeareacontext_EXPORTS -I/Users/<USER>/not_work/my-app/node_modules/react-native-safe-area-context/android/src/main/jni/. -I/Users/<USER>/not_work/my-app/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -I/Users/<USER>/not_work/my-app/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -I/Users/<USER>/not_work/my-app/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem /Users/<USER>/.gradle/caches/8.13/transforms/f7366e53586a12172ab374397325a4bf/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ee3752888ecf2d82fe4920d23f93a394/transformed/react-android-0.79.4-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ee3752888ecf2d82fe4920d23f93a394/transformed/react-android-0.79.4-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/Users/<USER>/not_work/my-app/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/ShadowNodes.cpp.o -c /Users/<USER>/not_work/my-app/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/ShadowNodes.cpp", "file": "/Users/<USER>/not_work/my-app/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/ShadowNodes.cpp"}, {"directory": "/Users/<USER>/not_work/my-app/android/app/.cxx/Debug/682h5i28/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -Dreact_codegen_safeareacontext_EXPORTS -I/Users/<USER>/not_work/my-app/node_modules/react-native-safe-area-context/android/src/main/jni/. -I/Users/<USER>/not_work/my-app/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -I/Users/<USER>/not_work/my-app/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -I/Users/<USER>/not_work/my-app/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem /Users/<USER>/.gradle/caches/8.13/transforms/f7366e53586a12172ab374397325a4bf/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ee3752888ecf2d82fe4920d23f93a394/transformed/react-android-0.79.4-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ee3752888ecf2d82fe4920d23f93a394/transformed/react-android-0.79.4-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/Users/<USER>/not_work/my-app/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/States.cpp.o -c /Users/<USER>/not_work/my-app/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/States.cpp", "file": "/Users/<USER>/not_work/my-app/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/States.cpp"}, {"directory": "/Users/<USER>/not_work/my-app/android/app/.cxx/Debug/682h5i28/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -Dreact_codegen_safeareacontext_EXPORTS -I/Users/<USER>/not_work/my-app/node_modules/react-native-safe-area-context/android/src/main/jni/. -I/Users/<USER>/not_work/my-app/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -I/Users/<USER>/not_work/my-app/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -I/Users/<USER>/not_work/my-app/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem /Users/<USER>/.gradle/caches/8.13/transforms/f7366e53586a12172ab374397325a4bf/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ee3752888ecf2d82fe4920d23f93a394/transformed/react-android-0.79.4-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ee3752888ecf2d82fe4920d23f93a394/transformed/react-android-0.79.4-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/Users/<USER>/not_work/my-app/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/safeareacontextJSI-generated.cpp.o -c /Users/<USER>/not_work/my-app/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/safeareacontextJSI-generated.cpp", "file": "/Users/<USER>/not_work/my-app/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/safeareacontextJSI-generated.cpp"}, {"directory": "/Users/<USER>/not_work/my-app/android/app/.cxx/Debug/682h5i28/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -Dreact_codegen_safeareacontext_EXPORTS -I/Users/<USER>/not_work/my-app/node_modules/react-native-safe-area-context/android/src/main/jni/. -I/Users/<USER>/not_work/my-app/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -I/Users/<USER>/not_work/my-app/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -I/Users/<USER>/not_work/my-app/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem /Users/<USER>/.gradle/caches/8.13/transforms/f7366e53586a12172ab374397325a4bf/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ee3752888ecf2d82fe4920d23f93a394/transformed/react-android-0.79.4-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ee3752888ecf2d82fe4920d23f93a394/transformed/react-android-0.79.4-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/Users/<USER>/not_work/my-app/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/safeareacontext-generated.cpp.o -c /Users/<USER>/not_work/my-app/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/safeareacontext-generated.cpp", "file": "/Users/<USER>/not_work/my-app/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/safeareacontext-generated.cpp"}, {"directory": "/Users/<USER>/not_work/my-app/android/app/.cxx/Debug/682h5i28/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -I/Users/<USER>/not_work/my-app/node_modules/react-native-screens/android/src/main/jni/. -I/Users/<USER>/not_work/my-app/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -I/Users/<USER>/not_work/my-app/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -I/Users/<USER>/not_work/my-app/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ee3752888ecf2d82fe4920d23f93a394/transformed/react-android-0.79.4-debug/prefab/modules/reactnative/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ee3752888ecf2d82fe4920d23f93a394/transformed/react-android-0.79.4-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/f7366e53586a12172ab374397325a4bf/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/not_work/my-app/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSFullWindowOverlayShadowNode.cpp.o -c /Users/<USER>/not_work/my-app/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSFullWindowOverlayShadowNode.cpp", "file": "/Users/<USER>/not_work/my-app/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSFullWindowOverlayShadowNode.cpp"}, {"directory": "/Users/<USER>/not_work/my-app/android/app/.cxx/Debug/682h5i28/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -I/Users/<USER>/not_work/my-app/node_modules/react-native-screens/android/src/main/jni/. -I/Users/<USER>/not_work/my-app/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -I/Users/<USER>/not_work/my-app/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -I/Users/<USER>/not_work/my-app/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ee3752888ecf2d82fe4920d23f93a394/transformed/react-android-0.79.4-debug/prefab/modules/reactnative/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ee3752888ecf2d82fe4920d23f93a394/transformed/react-android-0.79.4-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/f7366e53586a12172ab374397325a4bf/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/not_work/my-app/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSModalScreenShadowNode.cpp.o -c /Users/<USER>/not_work/my-app/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSModalScreenShadowNode.cpp", "file": "/Users/<USER>/not_work/my-app/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSModalScreenShadowNode.cpp"}, {"directory": "/Users/<USER>/not_work/my-app/android/app/.cxx/Debug/682h5i28/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -I/Users/<USER>/not_work/my-app/node_modules/react-native-screens/android/src/main/jni/. -I/Users/<USER>/not_work/my-app/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -I/Users/<USER>/not_work/my-app/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -I/Users/<USER>/not_work/my-app/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ee3752888ecf2d82fe4920d23f93a394/transformed/react-android-0.79.4-debug/prefab/modules/reactnative/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ee3752888ecf2d82fe4920d23f93a394/transformed/react-android-0.79.4-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/f7366e53586a12172ab374397325a4bf/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/not_work/my-app/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenShadowNode.cpp.o -c /Users/<USER>/not_work/my-app/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenShadowNode.cpp", "file": "/Users/<USER>/not_work/my-app/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenShadowNode.cpp"}, {"directory": "/Users/<USER>/not_work/my-app/android/app/.cxx/Debug/682h5i28/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -I/Users/<USER>/not_work/my-app/node_modules/react-native-screens/android/src/main/jni/. -I/Users/<USER>/not_work/my-app/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -I/Users/<USER>/not_work/my-app/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -I/Users/<USER>/not_work/my-app/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ee3752888ecf2d82fe4920d23f93a394/transformed/react-android-0.79.4-debug/prefab/modules/reactnative/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ee3752888ecf2d82fe4920d23f93a394/transformed/react-android-0.79.4-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/f7366e53586a12172ab374397325a4bf/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/not_work/my-app/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderConfigShadowNode.cpp.o -c /Users/<USER>/not_work/my-app/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderConfigShadowNode.cpp", "file": "/Users/<USER>/not_work/my-app/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderConfigShadowNode.cpp"}, {"directory": "/Users/<USER>/not_work/my-app/android/app/.cxx/Debug/682h5i28/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -I/Users/<USER>/not_work/my-app/node_modules/react-native-screens/android/src/main/jni/. -I/Users/<USER>/not_work/my-app/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -I/Users/<USER>/not_work/my-app/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -I/Users/<USER>/not_work/my-app/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ee3752888ecf2d82fe4920d23f93a394/transformed/react-android-0.79.4-debug/prefab/modules/reactnative/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ee3752888ecf2d82fe4920d23f93a394/transformed/react-android-0.79.4-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/f7366e53586a12172ab374397325a4bf/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/not_work/my-app/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderConfigState.cpp.o -c /Users/<USER>/not_work/my-app/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderConfigState.cpp", "file": "/Users/<USER>/not_work/my-app/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderConfigState.cpp"}, {"directory": "/Users/<USER>/not_work/my-app/android/app/.cxx/Debug/682h5i28/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -I/Users/<USER>/not_work/my-app/node_modules/react-native-screens/android/src/main/jni/. -I/Users/<USER>/not_work/my-app/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -I/Users/<USER>/not_work/my-app/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -I/Users/<USER>/not_work/my-app/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ee3752888ecf2d82fe4920d23f93a394/transformed/react-android-0.79.4-debug/prefab/modules/reactnative/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ee3752888ecf2d82fe4920d23f93a394/transformed/react-android-0.79.4-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/f7366e53586a12172ab374397325a4bf/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/not_work/my-app/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderSubviewShadowNode.cpp.o -c /Users/<USER>/not_work/my-app/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderSubviewShadowNode.cpp", "file": "/Users/<USER>/not_work/my-app/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderSubviewShadowNode.cpp"}, {"directory": "/Users/<USER>/not_work/my-app/android/app/.cxx/Debug/682h5i28/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -I/Users/<USER>/not_work/my-app/node_modules/react-native-screens/android/src/main/jni/. -I/Users/<USER>/not_work/my-app/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -I/Users/<USER>/not_work/my-app/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -I/Users/<USER>/not_work/my-app/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ee3752888ecf2d82fe4920d23f93a394/transformed/react-android-0.79.4-debug/prefab/modules/reactnative/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ee3752888ecf2d82fe4920d23f93a394/transformed/react-android-0.79.4-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/f7366e53586a12172ab374397325a4bf/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/not_work/my-app/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderSubviewState.cpp.o -c /Users/<USER>/not_work/my-app/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderSubviewState.cpp", "file": "/Users/<USER>/not_work/my-app/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderSubviewState.cpp"}, {"directory": "/Users/<USER>/not_work/my-app/android/app/.cxx/Debug/682h5i28/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -I/Users/<USER>/not_work/my-app/node_modules/react-native-screens/android/src/main/jni/. -I/Users/<USER>/not_work/my-app/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -I/Users/<USER>/not_work/my-app/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -I/Users/<USER>/not_work/my-app/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ee3752888ecf2d82fe4920d23f93a394/transformed/react-android-0.79.4-debug/prefab/modules/reactnative/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ee3752888ecf2d82fe4920d23f93a394/transformed/react-android-0.79.4-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/f7366e53586a12172ab374397325a4bf/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/not_work/my-app/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenState.cpp.o -c /Users/<USER>/not_work/my-app/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenState.cpp", "file": "/Users/<USER>/not_work/my-app/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenState.cpp"}, {"directory": "/Users/<USER>/not_work/my-app/android/app/.cxx/Debug/682h5i28/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -I/Users/<USER>/not_work/my-app/node_modules/react-native-screens/android/src/main/jni/. -I/Users/<USER>/not_work/my-app/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -I/Users/<USER>/not_work/my-app/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -I/Users/<USER>/not_work/my-app/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ee3752888ecf2d82fe4920d23f93a394/transformed/react-android-0.79.4-debug/prefab/modules/reactnative/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ee3752888ecf2d82fe4920d23f93a394/transformed/react-android-0.79.4-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/f7366e53586a12172ab374397325a4bf/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/rnscreens.cpp.o -c /Users/<USER>/not_work/my-app/node_modules/react-native-screens/android/src/main/jni/rnscreens.cpp", "file": "/Users/<USER>/not_work/my-app/node_modules/react-native-screens/android/src/main/jni/rnscreens.cpp"}, {"directory": "/Users/<USER>/not_work/my-app/android/app/.cxx/Debug/682h5i28/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -I/Users/<USER>/not_work/my-app/node_modules/react-native-screens/android/src/main/jni/. -I/Users/<USER>/not_work/my-app/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -I/Users/<USER>/not_work/my-app/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -I/Users/<USER>/not_work/my-app/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ee3752888ecf2d82fe4920d23f93a394/transformed/react-android-0.79.4-debug/prefab/modules/reactnative/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ee3752888ecf2d82fe4920d23f93a394/transformed/react-android-0.79.4-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/f7366e53586a12172ab374397325a4bf/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/not_work/my-app/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/ComponentDescriptors.cpp.o -c /Users/<USER>/not_work/my-app/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/ComponentDescriptors.cpp", "file": "/Users/<USER>/not_work/my-app/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/ComponentDescriptors.cpp"}, {"directory": "/Users/<USER>/not_work/my-app/android/app/.cxx/Debug/682h5i28/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -I/Users/<USER>/not_work/my-app/node_modules/react-native-screens/android/src/main/jni/. -I/Users/<USER>/not_work/my-app/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -I/Users/<USER>/not_work/my-app/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -I/Users/<USER>/not_work/my-app/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ee3752888ecf2d82fe4920d23f93a394/transformed/react-android-0.79.4-debug/prefab/modules/reactnative/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ee3752888ecf2d82fe4920d23f93a394/transformed/react-android-0.79.4-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/f7366e53586a12172ab374397325a4bf/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/not_work/my-app/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp.o -c /Users/<USER>/not_work/my-app/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp", "file": "/Users/<USER>/not_work/my-app/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp"}, {"directory": "/Users/<USER>/not_work/my-app/android/app/.cxx/Debug/682h5i28/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -I/Users/<USER>/not_work/my-app/node_modules/react-native-screens/android/src/main/jni/. -I/Users/<USER>/not_work/my-app/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -I/Users/<USER>/not_work/my-app/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -I/Users/<USER>/not_work/my-app/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ee3752888ecf2d82fe4920d23f93a394/transformed/react-android-0.79.4-debug/prefab/modules/reactnative/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ee3752888ecf2d82fe4920d23f93a394/transformed/react-android-0.79.4-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/f7366e53586a12172ab374397325a4bf/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/not_work/my-app/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/Props.cpp.o -c /Users/<USER>/not_work/my-app/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/Props.cpp", "file": "/Users/<USER>/not_work/my-app/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/Props.cpp"}, {"directory": "/Users/<USER>/not_work/my-app/android/app/.cxx/Debug/682h5i28/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -I/Users/<USER>/not_work/my-app/node_modules/react-native-screens/android/src/main/jni/. -I/Users/<USER>/not_work/my-app/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -I/Users/<USER>/not_work/my-app/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -I/Users/<USER>/not_work/my-app/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ee3752888ecf2d82fe4920d23f93a394/transformed/react-android-0.79.4-debug/prefab/modules/reactnative/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ee3752888ecf2d82fe4920d23f93a394/transformed/react-android-0.79.4-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/f7366e53586a12172ab374397325a4bf/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/not_work/my-app/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/ShadowNodes.cpp.o -c /Users/<USER>/not_work/my-app/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/ShadowNodes.cpp", "file": "/Users/<USER>/not_work/my-app/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/ShadowNodes.cpp"}, {"directory": "/Users/<USER>/not_work/my-app/android/app/.cxx/Debug/682h5i28/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -I/Users/<USER>/not_work/my-app/node_modules/react-native-screens/android/src/main/jni/. -I/Users/<USER>/not_work/my-app/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -I/Users/<USER>/not_work/my-app/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -I/Users/<USER>/not_work/my-app/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ee3752888ecf2d82fe4920d23f93a394/transformed/react-android-0.79.4-debug/prefab/modules/reactnative/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ee3752888ecf2d82fe4920d23f93a394/transformed/react-android-0.79.4-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/f7366e53586a12172ab374397325a4bf/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/not_work/my-app/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/States.cpp.o -c /Users/<USER>/not_work/my-app/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/States.cpp", "file": "/Users/<USER>/not_work/my-app/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/States.cpp"}, {"directory": "/Users/<USER>/not_work/my-app/android/app/.cxx/Debug/682h5i28/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -I/Users/<USER>/not_work/my-app/node_modules/react-native-screens/android/src/main/jni/. -I/Users/<USER>/not_work/my-app/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -I/Users/<USER>/not_work/my-app/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -I/Users/<USER>/not_work/my-app/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ee3752888ecf2d82fe4920d23f93a394/transformed/react-android-0.79.4-debug/prefab/modules/reactnative/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ee3752888ecf2d82fe4920d23f93a394/transformed/react-android-0.79.4-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/f7366e53586a12172ab374397325a4bf/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/not_work/my-app/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/rnscreensJSI-generated.cpp.o -c /Users/<USER>/not_work/my-app/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/rnscreensJSI-generated.cpp", "file": "/Users/<USER>/not_work/my-app/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/rnscreensJSI-generated.cpp"}, {"directory": "/Users/<USER>/not_work/my-app/android/app/.cxx/Debug/682h5i28/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -I/Users/<USER>/not_work/my-app/node_modules/react-native-webview/android/build/generated/source/codegen/jni/. -I/Users/<USER>/not_work/my-app/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec -isystem /Users/<USER>/.gradle/caches/8.13/transforms/f7366e53586a12172ab374397325a4bf/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ee3752888ecf2d82fe4920d23f93a394/transformed/react-android-0.79.4-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ee3752888ecf2d82fe4920d23f93a394/transformed/react-android-0.79.4-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/RNCWebViewSpec-generated.cpp.o -c /Users/<USER>/not_work/my-app/node_modules/react-native-webview/android/build/generated/source/codegen/jni/RNCWebViewSpec-generated.cpp", "file": "/Users/<USER>/not_work/my-app/node_modules/react-native-webview/android/build/generated/source/codegen/jni/RNCWebViewSpec-generated.cpp"}, {"directory": "/Users/<USER>/not_work/my-app/android/app/.cxx/Debug/682h5i28/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -I/Users/<USER>/not_work/my-app/node_modules/react-native-webview/android/build/generated/source/codegen/jni/. -I/Users/<USER>/not_work/my-app/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec -isystem /Users/<USER>/.gradle/caches/8.13/transforms/f7366e53586a12172ab374397325a4bf/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ee3752888ecf2d82fe4920d23f93a394/transformed/react-android-0.79.4-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ee3752888ecf2d82fe4920d23f93a394/transformed/react-android-0.79.4-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/ComponentDescriptors.cpp.o -c /Users/<USER>/not_work/my-app/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec/ComponentDescriptors.cpp", "file": "/Users/<USER>/not_work/my-app/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec/ComponentDescriptors.cpp"}, {"directory": "/Users/<USER>/not_work/my-app/android/app/.cxx/Debug/682h5i28/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -I/Users/<USER>/not_work/my-app/node_modules/react-native-webview/android/build/generated/source/codegen/jni/. -I/Users/<USER>/not_work/my-app/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec -isystem /Users/<USER>/.gradle/caches/8.13/transforms/f7366e53586a12172ab374397325a4bf/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ee3752888ecf2d82fe4920d23f93a394/transformed/react-android-0.79.4-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ee3752888ecf2d82fe4920d23f93a394/transformed/react-android-0.79.4-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/EventEmitters.cpp.o -c /Users/<USER>/not_work/my-app/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec/EventEmitters.cpp", "file": "/Users/<USER>/not_work/my-app/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec/EventEmitters.cpp"}, {"directory": "/Users/<USER>/not_work/my-app/android/app/.cxx/Debug/682h5i28/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -I/Users/<USER>/not_work/my-app/node_modules/react-native-webview/android/build/generated/source/codegen/jni/. -I/Users/<USER>/not_work/my-app/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec -isystem /Users/<USER>/.gradle/caches/8.13/transforms/f7366e53586a12172ab374397325a4bf/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ee3752888ecf2d82fe4920d23f93a394/transformed/react-android-0.79.4-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ee3752888ecf2d82fe4920d23f93a394/transformed/react-android-0.79.4-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/Props.cpp.o -c /Users/<USER>/not_work/my-app/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec/Props.cpp", "file": "/Users/<USER>/not_work/my-app/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec/Props.cpp"}, {"directory": "/Users/<USER>/not_work/my-app/android/app/.cxx/Debug/682h5i28/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -I/Users/<USER>/not_work/my-app/node_modules/react-native-webview/android/build/generated/source/codegen/jni/. -I/Users/<USER>/not_work/my-app/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec -isystem /Users/<USER>/.gradle/caches/8.13/transforms/f7366e53586a12172ab374397325a4bf/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ee3752888ecf2d82fe4920d23f93a394/transformed/react-android-0.79.4-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ee3752888ecf2d82fe4920d23f93a394/transformed/react-android-0.79.4-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/RNCWebViewSpecJSI-generated.cpp.o -c /Users/<USER>/not_work/my-app/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec/RNCWebViewSpecJSI-generated.cpp", "file": "/Users/<USER>/not_work/my-app/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec/RNCWebViewSpecJSI-generated.cpp"}, {"directory": "/Users/<USER>/not_work/my-app/android/app/.cxx/Debug/682h5i28/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -I/Users/<USER>/not_work/my-app/node_modules/react-native-webview/android/build/generated/source/codegen/jni/. -I/Users/<USER>/not_work/my-app/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec -isystem /Users/<USER>/.gradle/caches/8.13/transforms/f7366e53586a12172ab374397325a4bf/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ee3752888ecf2d82fe4920d23f93a394/transformed/react-android-0.79.4-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ee3752888ecf2d82fe4920d23f93a394/transformed/react-android-0.79.4-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/ShadowNodes.cpp.o -c /Users/<USER>/not_work/my-app/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec/ShadowNodes.cpp", "file": "/Users/<USER>/not_work/my-app/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec/ShadowNodes.cpp"}, {"directory": "/Users/<USER>/not_work/my-app/android/app/.cxx/Debug/682h5i28/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -I/Users/<USER>/not_work/my-app/node_modules/react-native-webview/android/build/generated/source/codegen/jni/. -I/Users/<USER>/not_work/my-app/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec -isystem /Users/<USER>/.gradle/caches/8.13/transforms/f7366e53586a12172ab374397325a4bf/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ee3752888ecf2d82fe4920d23f93a394/transformed/react-android-0.79.4-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ee3752888ecf2d82fe4920d23f93a394/transformed/react-android-0.79.4-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/States.cpp.o -c /Users/<USER>/not_work/my-app/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec/States.cpp", "file": "/Users/<USER>/not_work/my-app/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec/States.cpp"}, {"directory": "/Users/<USER>/not_work/my-app/android/app/.cxx/Debug/682h5i28/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -I/Users/<USER>/not_work/my-app/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/. -I/Users/<USER>/not_work/my-app/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/react/renderer/components/RNEdgeToEdge -isystem /Users/<USER>/.gradle/caches/8.13/transforms/f7366e53586a12172ab374397325a4bf/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ee3752888ecf2d82fe4920d23f93a394/transformed/react-android-0.79.4-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ee3752888ecf2d82fe4920d23f93a394/transformed/react-android-0.79.4-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/RNEdgeToEdge-generated.cpp.o -c /Users/<USER>/not_work/my-app/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/RNEdgeToEdge-generated.cpp", "file": "/Users/<USER>/not_work/my-app/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/RNEdgeToEdge-generated.cpp"}, {"directory": "/Users/<USER>/not_work/my-app/android/app/.cxx/Debug/682h5i28/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -I/Users/<USER>/not_work/my-app/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/. -I/Users/<USER>/not_work/my-app/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/react/renderer/components/RNEdgeToEdge -isystem /Users/<USER>/.gradle/caches/8.13/transforms/f7366e53586a12172ab374397325a4bf/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ee3752888ecf2d82fe4920d23f93a394/transformed/react-android-0.79.4-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ee3752888ecf2d82fe4920d23f93a394/transformed/react-android-0.79.4-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/ComponentDescriptors.cpp.o -c /Users/<USER>/not_work/my-app/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/react/renderer/components/RNEdgeToEdge/ComponentDescriptors.cpp", "file": "/Users/<USER>/not_work/my-app/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/react/renderer/components/RNEdgeToEdge/ComponentDescriptors.cpp"}, {"directory": "/Users/<USER>/not_work/my-app/android/app/.cxx/Debug/682h5i28/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -I/Users/<USER>/not_work/my-app/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/. -I/Users/<USER>/not_work/my-app/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/react/renderer/components/RNEdgeToEdge -isystem /Users/<USER>/.gradle/caches/8.13/transforms/f7366e53586a12172ab374397325a4bf/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ee3752888ecf2d82fe4920d23f93a394/transformed/react-android-0.79.4-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ee3752888ecf2d82fe4920d23f93a394/transformed/react-android-0.79.4-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/EventEmitters.cpp.o -c /Users/<USER>/not_work/my-app/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/react/renderer/components/RNEdgeToEdge/EventEmitters.cpp", "file": "/Users/<USER>/not_work/my-app/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/react/renderer/components/RNEdgeToEdge/EventEmitters.cpp"}, {"directory": "/Users/<USER>/not_work/my-app/android/app/.cxx/Debug/682h5i28/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -I/Users/<USER>/not_work/my-app/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/. -I/Users/<USER>/not_work/my-app/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/react/renderer/components/RNEdgeToEdge -isystem /Users/<USER>/.gradle/caches/8.13/transforms/f7366e53586a12172ab374397325a4bf/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ee3752888ecf2d82fe4920d23f93a394/transformed/react-android-0.79.4-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ee3752888ecf2d82fe4920d23f93a394/transformed/react-android-0.79.4-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/Props.cpp.o -c /Users/<USER>/not_work/my-app/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/react/renderer/components/RNEdgeToEdge/Props.cpp", "file": "/Users/<USER>/not_work/my-app/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/react/renderer/components/RNEdgeToEdge/Props.cpp"}, {"directory": "/Users/<USER>/not_work/my-app/android/app/.cxx/Debug/682h5i28/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -I/Users/<USER>/not_work/my-app/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/. -I/Users/<USER>/not_work/my-app/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/react/renderer/components/RNEdgeToEdge -isystem /Users/<USER>/.gradle/caches/8.13/transforms/f7366e53586a12172ab374397325a4bf/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ee3752888ecf2d82fe4920d23f93a394/transformed/react-android-0.79.4-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ee3752888ecf2d82fe4920d23f93a394/transformed/react-android-0.79.4-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/RNEdgeToEdgeJSI-generated.cpp.o -c /Users/<USER>/not_work/my-app/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/react/renderer/components/RNEdgeToEdge/RNEdgeToEdgeJSI-generated.cpp", "file": "/Users/<USER>/not_work/my-app/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/react/renderer/components/RNEdgeToEdge/RNEdgeToEdgeJSI-generated.cpp"}, {"directory": "/Users/<USER>/not_work/my-app/android/app/.cxx/Debug/682h5i28/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -I/Users/<USER>/not_work/my-app/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/. -I/Users/<USER>/not_work/my-app/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/react/renderer/components/RNEdgeToEdge -isystem /Users/<USER>/.gradle/caches/8.13/transforms/f7366e53586a12172ab374397325a4bf/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ee3752888ecf2d82fe4920d23f93a394/transformed/react-android-0.79.4-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ee3752888ecf2d82fe4920d23f93a394/transformed/react-android-0.79.4-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/ShadowNodes.cpp.o -c /Users/<USER>/not_work/my-app/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/react/renderer/components/RNEdgeToEdge/ShadowNodes.cpp", "file": "/Users/<USER>/not_work/my-app/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/react/renderer/components/RNEdgeToEdge/ShadowNodes.cpp"}, {"directory": "/Users/<USER>/not_work/my-app/android/app/.cxx/Debug/682h5i28/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -I/Users/<USER>/not_work/my-app/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/. -I/Users/<USER>/not_work/my-app/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/react/renderer/components/RNEdgeToEdge -isystem /Users/<USER>/.gradle/caches/8.13/transforms/f7366e53586a12172ab374397325a4bf/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ee3752888ecf2d82fe4920d23f93a394/transformed/react-android-0.79.4-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ee3752888ecf2d82fe4920d23f93a394/transformed/react-android-0.79.4-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/States.cpp.o -c /Users/<USER>/not_work/my-app/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/react/renderer/components/RNEdgeToEdge/States.cpp", "file": "/Users/<USER>/not_work/my-app/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/react/renderer/components/RNEdgeToEdge/States.cpp"}]