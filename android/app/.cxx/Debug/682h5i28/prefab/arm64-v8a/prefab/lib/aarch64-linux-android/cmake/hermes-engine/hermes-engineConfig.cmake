if(NOT TARGET hermes-engine::libhermes)
add_library(hermes-engine::libhermes SHARED IMPORTED)
set_target_properties(hermes-engine::libhermes PROPERTIES
    IMPORTED_LOCATION "/Users/<USER>/.gradle/caches/8.13/transforms/08e098abbe4d9b976b042791d1d5adf2/transformed/hermes-android-0.79.4-debug/prefab/modules/libhermes/libs/android.arm64-v8a/libhermes.so"
    INTERFACE_INCLUDE_DIRECTORIES "/Users/<USER>/.gradle/caches/8.13/transforms/08e098abbe4d9b976b042791d1d5adf2/transformed/hermes-android-0.79.4-debug/prefab/modules/libhermes/include"
    INTERFACE_LINK_LIBRARIES ""
)
endif()

