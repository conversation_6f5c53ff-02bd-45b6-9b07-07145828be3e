import React from 'react';
import {
  View,
  Text,
  FlatList,
  TouchableOpacity,
  Alert,
  StyleSheet,
  SafeAreaView,
  StatusBar,
} from 'react-native';

// Interface for our number item
interface NumberItem {
  id: number;
  value: number;
}

// Interface for the list item component props
interface NumberListItemProps {
  item: NumberItem;
  onPress: (number: number) => void;
}

// Component for individual list item
const NumberListItem: React.FC<NumberListItemProps> = ({ item, onPress }) => {
  return (
    <TouchableOpacity
      style={styles.listItem}
      onPress={() => onPress(item.value)}
      activeOpacity={0.7}
    >
      <View style={styles.itemContent}>
        <Text style={styles.numberText}>{item.value}</Text>
        <Text style={styles.labelText}>Number {item.value}</Text>
      </View>
    </TouchableOpacity>
  );
};

// Main component
export default function Index() {
  // Create array with numbers 1 to 20
  const numbers: NumberItem[] = Array.from({ length: 20 }, (_, index) => ({
    id: index + 1,
    value: index + 1,
  }));

  // Handle item press - show toast/alert
  const handleItemPress = (number: number) => {
    Alert.alert(
      'Number Tapped',
      `You tapped number ${number}!`,
      [{ text: 'OK', style: 'default' }],
      { cancelable: true }
    );
  };

  // Render item function for FlatList
  const renderItem = ({ item }: { item: NumberItem }) => (
    <NumberListItem item={item} onPress={handleItemPress} />
  );

  // Key extractor for FlatList
  const keyExtractor = (item: NumberItem) => item.id.toString();

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor="#f5f5f5" />

      {/* Header */}
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Number List</Text>
        <Text style={styles.headerSubtitle}>Tap any number to see a toast</Text>
      </View>

      {/* List */}
      <FlatList
        data={numbers}
        renderItem={renderItem}
        keyExtractor={keyExtractor}
        style={styles.list}
        contentContainerStyle={styles.listContent}
        showsVerticalScrollIndicator={false}
        ItemSeparatorComponent={() => <View style={styles.separator} />}
      />
    </SafeAreaView>
  );
}

// Styles
const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    backgroundColor: '#ffffff',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333333',
    marginBottom: 4,
  },
  headerSubtitle: {
    fontSize: 14,
    color: '#666666',
  },
  list: {
    flex: 1,
  },
  listContent: {
    padding: 16,
  },
  listItem: {
    backgroundColor: '#ffffff',
    borderRadius: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  itemContent: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 20,
  },
  numberText: {
    fontSize: 32,
    fontWeight: 'bold',
    color: '#2196F3',
    marginRight: 16,
    minWidth: 60,
    textAlign: 'center',
  },
  labelText: {
    fontSize: 18,
    color: '#333333',
    flex: 1,
  },
  separator: {
    height: 12,
  },
});
